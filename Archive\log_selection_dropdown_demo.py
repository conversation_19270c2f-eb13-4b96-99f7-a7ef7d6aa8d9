"""
Log Selection Dropdown Demo for Jupyter Notebooks
==================================================

This script demonstrates how to create interactive dropdown menus for selecting 
logs from a Petrel project using ipywidgets in Jupyter notebooks.

Based on the pythontool library utilities available in c:\Local\workflow\pythontool\

Usage:
1. Run this in a Jupyter notebook connected to Petrel
2. Use the dropdown widgets to select input and target logs
3. The selected logs can then be used in your ML workflows

Author: BKP_Team@PTM
Date: 2025-06-19
"""

import numpy as np
import pandas as pd
import ipywidgets as widgets
from IPython.display import display, clear_output
from cegalprizm.pythontool import PetrelConnection

def create_log_selection_interface():
    """
    Create interactive dropdown widgets for log selection from Petrel project.
    
    Returns:
        dict: Dictionary containing the widgets and available logs
    """
    
    # Connect to Petrel
    petrel = PetrelConnection()
    print(f'Connected to Petrel project: {petrel.get_current_project_name()}')
    
    # Get all available global well logs
    print('Scanning available global well logs...')
    available_logs = {}
    
    for log in petrel.global_well_logs:
        if hasattr(log, 'petrel_name'):
            available_logs[log.petrel_name] = log
        elif isinstance(log, list):
            for sub_log in log:
                if hasattr(sub_log, 'petrel_name'):
                    available_logs[sub_log.petrel_name] = sub_log
    
    log_names = sorted(available_logs.keys())
    print(f'Found {len(log_names)} global well logs')
    
    if not log_names:
        print('No global well logs found in the project!')
        return None
    
    # Create dropdown widgets
    input_logs_widget = widgets.SelectMultiple(
        options=log_names,
        value=tuple(['GR', 'RHOB', 'NPHI', 'Vp']) if all(log in log_names for log in ['GR', 'RHOB', 'NPHI', 'Vp']) else tuple(log_names[:min(4, len(log_names))]),
        description='Input Logs:',
        disabled=False,
        layout=widgets.Layout(width='400px', height='120px'),
        style={'description_width': 'initial'}
    )
    
    target_log_widget = widgets.Dropdown(
        options=log_names,
        value='Vs' if 'Vs' in log_names else log_names[0],
        description='Target Log:',
        disabled=False,
        layout=widgets.Layout(width='400px'),
        style={'description_width': 'initial'}
    )
    
    # Create well selection widget
    wells = [w for w in petrel.wells]
    well_names = [w.petrel_name for w in wells]
    
    well_widget = widgets.Dropdown(
        options=well_names,
        value=well_names[0] if well_names else None,
        description='Index Well:',
        disabled=False,
        layout=widgets.Layout(width='400px'),
        style={'description_width': 'initial'}
    )
    
    # Create output widget for displaying selections
    output_widget = widgets.Output()
    
    # Create update button
    update_button = widgets.Button(
        description='Update Selection',
        button_style='info',
        layout=widgets.Layout(width='200px')
    )
    
    def on_update_button_clicked(b):
        with output_widget:
            clear_output()
            print("Current Selection:")
            print(f"Index Well: {well_widget.value}")
            print(f"Input Logs: {list(input_logs_widget.value)}")
            print(f"Target Log: {target_log_widget.value}")
            print()
            
            # Show log availability for selected well
            if well_widget.value:
                selected_well = next((w for w in wells if w.petrel_name == well_widget.value), None)
                if selected_well:
                    well_log_names = [log.petrel_name for log in selected_well.logs]
                    print(f"Logs available in {well_widget.value}:")
                    for log_name in list(input_logs_widget.value) + [target_log_widget.value]:
                        status = "✓" if log_name in well_log_names else "✗"
                        print(f"  {status} {log_name}")
    
    update_button.on_click(on_update_button_clicked)
    
    # Display the interface
    interface = widgets.VBox([
        widgets.HTML('<h3>Petrel Log Selection Interface</h3>'),
        widgets.HTML('<b>Select Index Well:</b>'),
        well_widget,
        widgets.HTML('<br><b>Select Input Logs (hold Ctrl/Cmd for multiple):</b>'),
        input_logs_widget,
        widgets.HTML('<br><b>Select Target Log for Imputation:</b>'),
        target_log_widget,
        widgets.HTML('<br>'),
        update_button,
        output_widget
    ])
    
    display(interface)
    
    # Trigger initial update
    on_update_button_clicked(None)
    
    # Return the widgets and data for programmatic access
    return {
        'input_logs': input_logs_widget,
        'target_log': target_log_widget,
        'well': well_widget,
        'available_logs': available_logs,
        'wells': wells,
        'petrel': petrel,
        'output': output_widget,
        'interface': interface
    }

def get_selected_logs_data(widgets_dict):
    """
    Extract log data for the selected well and logs.
    
    Args:
        widgets_dict: Dictionary returned by create_log_selection_interface()
        
    Returns:
        pandas.DataFrame: Log data for the selected well and logs
    """
    
    if not widgets_dict:
        return None
    
    petrel = widgets_dict['petrel']
    wells = widgets_dict['wells']
    available_logs = widgets_dict['available_logs']
    
    # Get selected values
    selected_well_name = widgets_dict['well'].value
    selected_input_logs = list(widgets_dict['input_logs'].value)
    selected_target_log = widgets_dict['target_log'].value
    
    # Find the selected well
    selected_well = next((w for w in wells if w.petrel_name == selected_well_name), None)
    if not selected_well:
        print(f"Well {selected_well_name} not found!")
        return None
    
    # Get the log objects
    all_log_names = selected_input_logs + [selected_target_log]
    log_objects = []
    
    for log_name in all_log_names:
        if log_name in available_logs:
            log_objects.append(available_logs[log_name])
        else:
            print(f"Warning: Log {log_name} not found in available logs")
    
    if not log_objects:
        print("No valid log objects found!")
        return None
    
    # Get the data
    try:
        df = selected_well.logs_dataframe(log_objects)
        df['WELL'] = selected_well.petrel_name
        df.reset_index(drop=False, inplace=True)  # MD becomes column
        
        print(f"Successfully loaded data: {len(df)} samples")
        print(f"Available columns: {list(df.columns)}")
        
        return df
        
    except Exception as e:
        print(f"Error loading log data: {str(e)}")
        return None

# Example usage
if __name__ == "__main__":
    print("Creating log selection interface...")
    widgets_dict = create_log_selection_interface()
    
    if widgets_dict:
        print("\nTo get the selected log data, run:")
        print("df = get_selected_logs_data(widgets_dict)")
        print("\nTo access individual selections:")
        print("selected_input_logs = list(widgets_dict['input_logs'].value)")
        print("selected_target_log = widgets_dict['target_log'].value")
        print("selected_well = widgets_dict['well'].value")
