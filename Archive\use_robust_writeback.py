"""
Standalone Script for Using Robust Write-Back Function
=====================================================

This script demonstrates how to use the robust write_back_to_petrel function
to write VP_COREL_ML_repredicted log data back to Petrel.

Usage:
    1. Ensure you have your results DataFrame with VP_COREL_ML_repredicted column
    2. Run this script in Python Tool Pro environment
    3. Follow the prompts to select wells and execute write-back

Requirements:
    - Active Petrel connection
    - Results DataFrame with ML predictions
    - Selected wells and target log context
"""

import pandas as pd
import numpy as np

# Required imports for Petrel integration
try:
    from cegalprizm.pythontool import PetrelConnection, Well, GlobalWellLog, WellLog, DiscreteGlobalWellLog
    print("✓ Petrel imports successful")
except ImportError as e:
    print(f"✗ Petrel imports failed: {e}")
    print("Ensure you're running this in Python Tool Pro environment")
    exit(1)

# Establish Petrel connection
try:
    petrel = PetrelConnection()
    print(f"✓ Connected to Petrel project: {petrel.get_current_project_name()}")
except Exception as e:
    print(f"✗ Failed to connect to Petrel: {e}")
    exit(1)

# Import the robust write-back functions
print("Importing robust write-back functions...")

def write_back_log_robust(petrel, log_name: str, log_to_clone, well, md: list, values: list, template=None):
    """
    Robust function to write log data back to a well in Petrel.
    Based on the proven implementation from Missing_Log_Imputation_Petrel_Interactive.py
    """
    print(f"DEBUG: Starting write_back_log_robust for {log_name} in well {well.petrel_name}")
    print(f"DEBUG: MD values count: {len(md)}, Log values count: {len(values)}")
    print(f"DEBUG: Log to clone: {log_to_clone.petrel_name}")

    try:
        # Helper to handle Petrel collections that may contain lists
        def get_object_list(collection):
            object_list = []
            for item in collection:
                if isinstance(item, list):
                    object_list.extend(item)
                else:
                    object_list.append(item)
            return object_list

        print("DEBUG: Determining global log collection type")
        # Determine which global log collection to search
        if isinstance(log_to_clone, DiscreteGlobalWellLog):
            gwl_collection = petrel.discrete_global_well_logs
            print("DEBUG: Using discrete global well logs collection")
        else:
            gwl_collection = petrel.global_well_logs
            print("DEBUG: Using continuous global well logs collection")

        print(f"DEBUG: Searching for existing global log: {log_name}")
        gwl = [i for i in get_object_list(gwl_collection) if i.petrel_name == log_name]

        # If global well log doesn't exist, create a new one.
        if not gwl:
            print(f'Creating new global well log: {log_name}')
            print("DEBUG: Cloning global log from template")
            global_log = log_to_clone.clone(name_of_clone=log_name, template=template)
            gwl = [global_log]
            print("DEBUG: New global log created successfully")
        else:
            print(f"DEBUG: Found existing global log: {log_name}")

        print("DEBUG: Checking for existing well log in target well")
        # Check if the well already has this log
        well_log = [i for i in get_object_list(well.logs) if i.petrel_name == log_name]

        # If well has the log, overwrite its values.
        if len(well_log) == 1:
            print("DEBUG: Well log exists, overwriting values")
            well_log[0].readonly = False
            well_log[0].set_values(md, values)
            print(f"Values for {log_name} overwritten for {well.petrel_name}")
            print("DEBUG: Well log values overwritten successfully")
        # If well does not have this log, create it and set its values.
        elif not well_log:
            print(f'Creating new well log: {log_name} for well {well.petrel_name}')
            print("DEBUG: Creating new well log from global log")
            new_log = gwl[0].create_well_log(well)
            new_log.readonly = False
            new_log.set_values(md, values)
            print(f"New well log {log_name} created for well {well.petrel_name}")
            print("DEBUG: New well log created and values set successfully")
        else:
            print(f"WARNING: Multiple well logs found with name {log_name} in well {well.petrel_name}")

        print(f"DEBUG: write_back_log_robust completed successfully for {log_name}")
        return True

    except Exception as e:
        print(f"ERROR: Failed to write back log {log_name} for well {well.petrel_name}: {str(e)}")
        print(f"DEBUG: Exception type: {type(e)}")
        print(f"DEBUG: MD values sample: {md[:5] if len(md) > 5 else md}")
        print(f"DEBUG: Log values sample: {values[:5] if len(values) > 5 else values}")
        import traceback
        print(f"DEBUG: Traceback: {traceback.format_exc()}")
        return False


def write_back_to_petrel_robust(petrel, results_df, log_name_in_results, selected_wells, clone_from_log_name, new_log_name=None):
    """
    Enhanced write-back function using robust implementation pattern.
    
    Args:
        petrel: PetrelConnection instance
        results_df: DataFrame containing the results
        log_name_in_results: Column name in results_df to write back
        selected_wells: List of Well objects
        clone_from_log_name: Name of template log to clone from
        new_log_name: Name for the new log (auto-generated if None)
    """
    # Auto-generate new log name if not provided
    if new_log_name is None:
        if '_repredicted' in log_name_in_results:
            new_log_name = log_name_in_results  # Keep the same name
        elif '_imputed' in log_name_in_results:
            new_log_name = log_name_in_results.replace('_imputed', '_ML_imputed')
        elif '_pred' in log_name_in_results:
            new_log_name = log_name_in_results.replace('_pred', '_ML_predicted')
        else:
            new_log_name = f'{log_name_in_results}_ML'

    print(f'Writing back {log_name_in_results} as {new_log_name} to {len(selected_wells)} wells...')
    print(f'Using robust write-back implementation pattern')
    
    # Find the global log to use as template
    print(f"DEBUG: Searching for global log template: {clone_from_log_name}")
    
    def find_global_well_log_by_name(name):
        """Helper function to find global well log by name"""
        for item in petrel.global_well_logs:
            if isinstance(item, list):
                for obj in item:
                    if hasattr(obj, 'petrel_name') and obj.petrel_name == name:
                        return obj
            elif hasattr(item, 'petrel_name') and item.petrel_name == name:
                return item
        return None
    
    log_to_clone = find_global_well_log_by_name(clone_from_log_name)
    if log_to_clone is None:
        print(f"ERROR: Could not find global well log with name: {clone_from_log_name}")
        return False
    
    print(f"DEBUG: Found global log template: {log_to_clone.petrel_name}")
    
    success_count = 0
    total_wells = len(selected_wells)
    
    for w in selected_wells:
        print(f'\nProcessing well: {w.petrel_name} ({success_count + 1}/{total_wells})')
        
        # Filter data for this well
        well_df = results_df[results_df['WELL'] == w.petrel_name]
        if well_df.empty:
            print(f'  ⚠ No data for well {w.petrel_name}, skipping')
            continue
        
        # Extract MD and values, ensuring no NaN values
        md_values = well_df['MD'].values
        log_values = well_df[log_name_in_results].values
        
        # Clean NaN values
        valid_mask = ~np.isnan(md_values) & ~np.isnan(log_values)
        md_clean = md_values[valid_mask].tolist()
        values_clean = log_values[valid_mask].tolist()
        
        if not md_clean:
            print(f'  ⚠ No valid data points for well {w.petrel_name}, skipping')
            continue
            
        print(f'  Valid data points: {len(md_clean)}')
        
        # Use robust write-back function
        try:
            success = write_back_log_robust(
                petrel=petrel,
                log_name=new_log_name,
                log_to_clone=log_to_clone,
                well=w,
                md=md_clean,
                values=values_clean,
                template=None
            )
            
            if success:
                print(f'  ✓ Successfully updated {new_log_name} for {w.petrel_name}')
                success_count += 1
            else:
                print(f'  ✗ Failed to update {new_log_name} for {w.petrel_name}')
                
        except Exception as e:
            print(f'  ✗ Error updating {new_log_name} for {w.petrel_name}: {str(e)}')
    
    print(f'\n{"="*60}')
    print(f'WRITE-BACK COMPLETED: {success_count}/{total_wells} wells updated successfully')
    print(f'New log name: {new_log_name}')
    print(f'{"="*60}')
    
    return success_count > 0


# Example usage function
def example_usage():
    """
    Example of how to use the robust write-back function for VP_COREL_ML_repredicted data
    """
    print("\n" + "="*60)
    print("EXAMPLE USAGE FOR VP_COREL_ML_repredicted DATA")
    print("="*60)
    
    # Step 1: Get available wells
    print("Step 1: Getting available wells...")
    try:
        wells = list(petrel.wells)
        well_names = [w.petrel_name for w in wells]
        print(f"Found {len(wells)} wells: {well_names[:5]}..." if len(wells) > 5 else f"Found {len(wells)} wells: {well_names}")
    except Exception as e:
        print(f"Error getting wells: {e}")
        return
    
    # Step 2: Create sample results DataFrame (replace with your actual results)
    print("\nStep 2: Creating sample results DataFrame...")
    sample_results = pd.DataFrame({
        'WELL': [well_names[0]] * 100 if wells else ['SAMPLE_WELL'] * 100,
        'MD': np.arange(100) * 10.0,
        'VP_COREL_ML_repredicted': np.random.normal(3000, 500, 100)
    })
    print(f"Sample results shape: {sample_results.shape}")
    print(f"Columns: {list(sample_results.columns)}")
    
    # Step 3: Select target wells (first 4 wells as example)
    print("\nStep 3: Selecting target wells...")
    target_wells = wells[:4] if len(wells) >= 4 else wells
    print(f"Selected {len(target_wells)} wells for write-back")
    
    # Step 4: Execute write-back
    print("\nStep 4: Executing write-back...")
    print("NOTE: This is a demonstration. Replace 'sample_results' with your actual results DataFrame.")
    
    # Uncomment the line below to execute actual write-back
    # success = write_back_to_petrel_robust(
    #     petrel=petrel,
    #     results_df=sample_results,  # Replace with your actual results
    #     log_name_in_results='VP_COREL_ML_repredicted',
    #     selected_wells=target_wells,
    #     clone_from_log_name='VP',  # or 'VP_COREL' depending on your template
    #     new_log_name='VP_COREL_ML_repredicted'
    # )
    
    print("To execute actual write-back, uncomment the function call above and provide your results DataFrame.")


if __name__ == "__main__":
    print("Robust Write-Back Function Ready!")
    print("Functions available:")
    print("- write_back_log_robust(): Core robust write-back function")
    print("- write_back_to_petrel_robust(): Enhanced wrapper function")
    
    # Run example
    example_usage()
    
    print(f"\nConnection to {petrel.get_current_project_name()} is active.")
    print("You can now use the robust write-back functions with your actual data.")
