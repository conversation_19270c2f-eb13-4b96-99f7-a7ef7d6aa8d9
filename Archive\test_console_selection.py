"""
Test script for console-based selection functions
This script tests the console selection functions with sample data.
"""

from cegalprizm.pythontool import PetrelConnection

def console_select_multiple(options, prompt, default_selections=None, max_selections=None):
    """
    Console-based multiple selection function.
    
    Args:
        options: List of available options
        prompt: Description of what to select
        default_selections: List of default selections (by name)
        max_selections: Maximum number of selections allowed
    
    Returns:
        List of selected option names
    """
    if not options:
        print("No options available for selection.")
        return []
    
    print(f"\n{prompt}")
    print("Available options:")
    for i, option in enumerate(options, 1):
        print(f"  {i}. {option}")
    
    # Set up defaults
    default_indices = []
    if default_selections:
        for default in default_selections:
            if default in options:
                default_indices.append(options.index(default) + 1)
    
    if default_indices:
        print(f"\nDefault selections: {', '.join([str(i) for i in default_indices])}")
    
    print(f"\nEnter your selections:")
    print("- Enter numbers separated by commas (e.g., 1,3,5)")
    print("- Enter 'all' to select all options")
    print("- Press Enter to use defaults (if available)")
    if max_selections:
        print(f"- Maximum {max_selections} selections allowed")
    
    while True:
        try:
            user_input = input("Selection: ").strip()
            
            # Use defaults if empty input and defaults exist
            if not user_input and default_indices:
                selected_indices = default_indices
                break
            
            # Select all
            if user_input.lower() == 'all':
                if max_selections and len(options) > max_selections:
                    print(f"Cannot select all - maximum {max_selections} selections allowed.")
                    continue
                selected_indices = list(range(1, len(options) + 1))
                break
            
            # Parse comma-separated numbers
            if user_input:
                selected_indices = [int(x.strip()) for x in user_input.split(',')]
                
                # Validate selections
                if any(i < 1 or i > len(options) for i in selected_indices):
                    print(f"Invalid selection. Please enter numbers between 1 and {len(options)}")
                    continue
                
                if max_selections and len(selected_indices) > max_selections:
                    print(f"Too many selections. Maximum {max_selections} allowed.")
                    continue
                
                break
            else:
                print("Please enter a selection or press Enter for defaults.")
                
        except ValueError:
            print("Invalid input. Please enter numbers separated by commas.")
    
    # Convert indices to option names
    selected_options = [options[i-1] for i in selected_indices]
    print(f"Selected: {', '.join(selected_options)}")
    return selected_options


def console_select_single(options, prompt, default_selection=None):
    """
    Console-based single selection function.
    
    Args:
        options: List of available options
        prompt: Description of what to select
        default_selection: Default selection (by name)
    
    Returns:
        Selected option name
    """
    if not options:
        print("No options available for selection.")
        return None
    
    print(f"\n{prompt}")
    print("Available options:")
    for i, option in enumerate(options, 1):
        print(f"  {i}. {option}")
    
    # Set up default
    default_index = None
    if default_selection and default_selection in options:
        default_index = options.index(default_selection) + 1
        print(f"\nDefault selection: {default_index}")
    
    print(f"\nEnter your selection:")
    print("- Enter a number (1-{})".format(len(options)))
    print("- Press Enter to use default (if available)")
    
    while True:
        try:
            user_input = input("Selection: ").strip()
            
            # Use default if empty input and default exists
            if not user_input and default_index:
                selected_index = default_index
                break
            
            # Parse number
            if user_input:
                selected_index = int(user_input)
                
                # Validate selection
                if selected_index < 1 or selected_index > len(options):
                    print(f"Invalid selection. Please enter a number between 1 and {len(options)}")
                    continue
                
                break
            else:
                print("Please enter a selection or press Enter for default.")
                
        except ValueError:
            print("Invalid input. Please enter a number.")
    
    # Convert index to option name
    selected_option = options[selected_index-1]
    print(f"Selected: {selected_option}")
    return selected_option


def test_with_petrel_data():
    """Test with actual Petrel data"""
    print("="*60)
    print("TESTING WITH PETREL DATA")
    print("="*60)
    
    try:
        # Connect to Petrel
        petrel = PetrelConnection()
        print(f'✓ Connected to Petrel project: {petrel.get_current_project_name()}')
        
        # Get wells
        wells = list(petrel.wells)
        well_names = [w.petrel_name for w in wells]
        print(f'Found {len(wells)} wells')
        
        # Get logs
        available_logs = {}
        for log in petrel.global_well_logs:
            if hasattr(log, 'petrel_name'):
                available_logs[log.petrel_name] = log
        
        log_names = sorted(available_logs.keys())
        print(f'Found {len(log_names)} global well logs')
        
        # Test well selection (limit to first 10 for testing)
        test_wells = well_names[:10]
        selected_wells = console_select_multiple(
            options=test_wells,
            prompt="Select Wells for Testing (first 10 shown):",
            default_selections=test_wells[:3],
            max_selections=5
        )
        
        # Test log selection (limit to first 15 for testing)
        test_logs = log_names[:15]
        selected_logs = console_select_multiple(
            options=test_logs,
            prompt="Select Input Logs for Testing (first 15 shown):",
            default_selections=['GR', 'RHOB'] if 'GR' in test_logs and 'RHOB' in test_logs else test_logs[:2],
            max_selections=None
        )
        
        # Test single selection
        selected_target = console_select_single(
            options=test_logs,
            prompt="Select Target Log for Testing:",
            default_selection='Vs' if 'Vs' in test_logs else test_logs[0]
        )
        
        print("\n" + "="*60)
        print("TEST RESULTS")
        print("="*60)
        print(f"Selected wells: {selected_wells}")
        print(f"Selected input logs: {selected_logs}")
        print(f"Selected target log: {selected_target}")
        print("✓ Console selection functions working correctly!")
        
    except Exception as e:
        print(f"✗ Error during testing: {str(e)}")


if __name__ == "__main__":
    print("Testing console-based selection functions...")
    test_with_petrel_data()
