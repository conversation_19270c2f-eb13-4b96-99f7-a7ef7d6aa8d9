"""
Test script with automatic selection (no user input required)
This script tests the data loading with automatic default selections.
"""

from cegalprizm.pythontool import PetrelConnection
import pandas as pd
import numpy as np

def auto_select_multiple(options, default_selections=None, max_selections=None):
    """Auto-select with defaults (no user input)"""
    if not options:
        return []
    
    if default_selections:
        selected = [opt for opt in default_selections if opt in options]
        if selected:
            return selected
    
    # Fallback to first few options
    max_select = min(max_selections or 5, len(options))
    return options[:max_select]

def auto_select_single(options, default_selection=None):
    """Auto-select single option with default (no user input)"""
    if not options:
        return None
    
    if default_selection and default_selection in options:
        return default_selection
    
    return options[0]

def test_data_loading():
    """Test the complete data loading workflow with auto-selection"""
    print("="*60)
    print("TESTING DATA LOADING WITH AUTO-SELECTION")
    print("="*60)
    
    try:
        # Connect to Petrel
        petrel = PetrelConnection()
        print(f'✓ Connected to Petrel project: {petrel.get_current_project_name()}')
        
        # Get wells
        print('\nScanning available wells...')
        wells = list(petrel.wells)
        well_names = [w.petrel_name for w in wells]
        print(f'Found {len(wells)} wells')
        print(f'First few wells: {well_names[:5]}')
        
        # Get logs
        print('\nScanning available global well logs...')
        available_logs = {}
        for log in petrel.global_well_logs:
            if hasattr(log, 'petrel_name'):
                available_logs[log.petrel_name] = log
        
        log_names = sorted(available_logs.keys())
        print(f'Found {len(log_names)} global well logs')
        print(f'First few logs: {log_names[:10]}')
        
        # Auto-select wells (first 3)
        selected_well_names = auto_select_multiple(
            options=well_names,
            default_selections=well_names[:3],
            max_selections=3
        )
        print(f'\n✓ Auto-selected wells: {selected_well_names}')
        
        # Auto-select input logs
        default_input_logs = ['GR', 'RHOB', 'NPHI', 'Vp']
        selected_input_logs = auto_select_multiple(
            options=log_names,
            default_selections=default_input_logs,
            max_selections=None
        )
        print(f'✓ Auto-selected input logs: {selected_input_logs}')
        
        # Auto-select target log
        selected_target_log = auto_select_single(
            options=log_names,
            default_selection='Vs'
        )
        print(f'✓ Auto-selected target log: {selected_target_log}')
        
        # Get selected wells objects
        selected_wells = [w for w in wells if w.petrel_name in selected_well_names]
        
        # Combine all log names
        LOG_NAMES = selected_input_logs + [selected_target_log]
        print(f'✓ All logs to process: {LOG_NAMES}')
        
        # Find global well logs by name
        def find_global_well_logs_by_names(names):
            found_logs = []
            for name in names:
                if name in available_logs:
                    found_logs.append(available_logs[name])
                else:
                    print(f'Warning: Log {name} not found in available logs')
            return found_logs
        
        logs = find_global_well_logs_by_names(LOG_NAMES)
        print(f'✓ Using {len(logs)} global logs: {[g.petrel_name for g in logs]}')
        
        # Test data loading from first well
        if selected_wells and logs:
            print(f'\n--- Testing data loading from first well: {selected_wells[0].petrel_name} ---')
            try:
                df = selected_wells[0].logs_dataframe(logs)
                if not df.empty:
                    print(f'✓ Successfully loaded data: {df.shape}')
                    print(f'Columns: {list(df.columns)}')
                    print(f'Index (MD) range: {df.index.min():.1f} - {df.index.max():.1f}')
                    
                    # Check data coverage
                    coverage = 1.0 - df.isna().mean()
                    print('\nData coverage per log:')
                    for log_name, cov in coverage.items():
                        print(f'  {log_name}: {cov:.2%}')
                else:
                    print('⚠ No data found for this well')
            except Exception as e:
                print(f'✗ Error loading data: {str(e)}')
        
        print(f'\n✓ Test completed successfully!')
        print(f'Wells available: {len(wells)}')
        print(f'Logs available: {len(log_names)}')
        print(f'Selected wells: {len(selected_wells)}')
        print(f'Selected logs: {len(logs)}')
        
        return {
            'wells': wells,
            'selected_wells': selected_wells,
            'selected_well_names': selected_well_names,
            'logs': logs,
            'log_names': log_names,
            'selected_input_logs': selected_input_logs,
            'selected_target_log': selected_target_log,
            'available_logs': available_logs
        }
        
    except Exception as e:
        print(f'✗ Error during testing: {str(e)}')
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    print("Testing data loading with automatic selection...")
    result = test_data_loading()
    
    if result:
        print("\n" + "="*60)
        print("SUCCESS: All components working correctly!")
        print("The main script should work with console-based selection.")
        print("="*60)
    else:
        print("\n" + "="*60)
        print("FAILURE: Issues detected that need to be resolved.")
        print("="*60)
