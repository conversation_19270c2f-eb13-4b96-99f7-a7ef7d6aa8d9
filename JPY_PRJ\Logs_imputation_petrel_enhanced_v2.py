# Missing Logs Imputation – Petrel-connected workflow (Enhanced Version)
# Enhanced with hyperparameter input system and well separation features - 2025-06-20
# This enhanced version adds:
# 1. Interactive hyperparameter configuration for ML models
# 2. Well separation system (training-only vs prediction wells)
# 3. Maintains all existing functionality from optimized version

import numpy as np
import pandas as pd
import warnings
warnings.filterwarnings('ignore')

# ML libraries
from xgboost import XGBRegressor
from lightgbm import LGBMRegressor
from catboost import CatBoostRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, r2_score

# Plotting
import plotly.express as px

# Petrel connection
from cegalprizm.pythontool import PetrelConnection, DiscreteGlobalWellLog

# Initialize connection and configuration
petrel = PetrelConnection()
print(f'Connected to Petrel project: {petrel.get_current_project_name()}')

AUTO_MODE = False  # Set to True for automatic selection
PREDICTION_MODES = {1: "Fill Missing Values Only", 2: "Cross-Validation Test", 3: "Re-predict Entire Log"}

# Enhanced hyperparameter configuration system
def get_hyperparameter_config(auto_mode=False):
    """Interactive hyperparameter configuration for ML models."""
    print(f"\n{'='*60}")
    print("HYPERPARAMETER CONFIGURATION")
    print('='*60)
    
    # Enhanced hyperparameters with optional tuning parameters and recommendations
    default_config = {
        'xgboost': {
            # Core parameters (from reference file)
            'n_estimators': 300,
            'tree_method': 'gpu_hist',
            'learning_rate': 0.05,
            'early_stopping_rounds': 100,
            'random_state': 42,
            # Optional tuning parameters with recommended defaults
            'max_depth': 6,
            'subsample': 1.0,
            'colsample_bytree': 1.0,
            'reg_alpha': 0,
            'reg_lambda': 1,
            'gamma': 0
        },
        'lightgbm': {
            # Core parameters (from reference file)
            'device': 'gpu',
            'n_estimators': 300,
            'random_state': 42,
            # Optional tuning parameters with recommended defaults
            'max_depth': -1,
            'learning_rate': 0.1,
            'subsample': 1.0,
            'colsample_bytree': 1.0,
            'reg_alpha': 0,
            'reg_lambda': 0,
            'min_child_samples': 20
        },
        'catboost': {
            # Core parameters (from reference file)
            'task_type': 'GPU',
            'early_stopping_rounds': 100,
            'verbose': 0,
            'random_state': 42,
            # Optional tuning parameters with recommended defaults
            'iterations': 1000,
            'learning_rate': None,  # Auto-determined by CatBoost
            'depth': 6,
            'l2_leaf_reg': 3,
            'subsample': 1.0,
            'colsample_bylevel': 1.0,
            'random_strength': 1
        }
    }
    
    if auto_mode:
        print("Auto-mode: Using default hyperparameters")
        for model, params in default_config.items():
            print(f"  {model.upper()}: {params}")
        return default_config
    
    print("Configure hyperparameters for each model:")
    print("Press Enter to use default values, or enter new values")
    
    config = {}
    
    # XGBoost configuration with enhanced options and recommendations
    print(f"\n--- XGBoost Configuration ---")
    print("XGBoost is a powerful gradient boosting framework optimized for speed and performance.")
    config['xgboost'] = {}

    # Core tunable parameters
    config['xgboost']['n_estimators'] = get_int_input(
        "Number of estimators (trees) - More trees = better fit but slower training",
        default_config['xgboost']['n_estimators'], 50, 2000)

    config['xgboost']['learning_rate'] = get_float_input(
        "Learning rate - Lower = more conservative, higher = faster convergence",
        default_config['xgboost']['learning_rate'], 0.01, 0.3)

    # Optional advanced parameters
    print(f"\n--- XGBoost Advanced Parameters (Optional) ---")
    print("Press Enter to use recommended defaults, or specify custom values:")

    config['xgboost']['max_depth'] = get_int_input(
        "Max tree depth - Deeper trees can capture more patterns but may overfit (recommended: 6)",
        default_config['xgboost']['max_depth'], 3, 15)

    config['xgboost']['subsample'] = get_float_input(
        "Subsample ratio - Fraction of samples used per tree (recommended: 0.8-1.0)",
        default_config['xgboost']['subsample'], 0.5, 1.0)

    config['xgboost']['colsample_bytree'] = get_float_input(
        "Column sampling - Fraction of features used per tree (recommended: 0.8-1.0)",
        default_config['xgboost']['colsample_bytree'], 0.5, 1.0)

    config['xgboost']['reg_alpha'] = get_float_input(
        "L1 regularization - Helps with feature selection (recommended: 0-1)",
        default_config['xgboost']['reg_alpha'], 0, 10)

    config['xgboost']['reg_lambda'] = get_float_input(
        "L2 regularization - Helps prevent overfitting (recommended: 1)",
        default_config['xgboost']['reg_lambda'], 0, 10)

    config['xgboost']['gamma'] = get_float_input(
        "Gamma (min split loss) - Higher values make model more conservative (recommended: 0)",
        default_config['xgboost']['gamma'], 0, 5)

    # Fixed parameters from reference file (essential for performance)
    config['xgboost']['tree_method'] = default_config['xgboost']['tree_method']
    config['xgboost']['early_stopping_rounds'] = default_config['xgboost']['early_stopping_rounds']
    config['xgboost']['random_state'] = default_config['xgboost']['random_state']
    
    # LightGBM configuration with enhanced options and recommendations
    print(f"\n--- LightGBM Configuration ---")
    print("LightGBM is a fast gradient boosting framework with excellent default parameters.")
    config['lightgbm'] = {}

    # Core tunable parameters
    config['lightgbm']['n_estimators'] = get_int_input(
        "Number of estimators (trees) - More trees = better fit but slower training",
        default_config['lightgbm']['n_estimators'], 50, 2000)

    # Optional advanced parameters
    print(f"\n--- LightGBM Advanced Parameters (Optional) ---")
    print("Press Enter to use recommended defaults, or specify custom values:")

    config['lightgbm']['learning_rate'] = get_float_input(
        "Learning rate - Lower = more conservative, higher = faster convergence (recommended: 0.1)",
        default_config['lightgbm']['learning_rate'], 0.01, 0.3)

    config['lightgbm']['max_depth'] = get_int_input(
        "Max tree depth - -1 for no limit, 6-10 for controlled depth (recommended: -1)",
        default_config['lightgbm']['max_depth'], -1, 15)

    config['lightgbm']['subsample'] = get_float_input(
        "Subsample ratio - Fraction of samples used per tree (recommended: 0.8-1.0)",
        default_config['lightgbm']['subsample'], 0.5, 1.0)

    config['lightgbm']['colsample_bytree'] = get_float_input(
        "Column sampling - Fraction of features used per tree (recommended: 0.8-1.0)",
        default_config['lightgbm']['colsample_bytree'], 0.5, 1.0)

    config['lightgbm']['reg_alpha'] = get_float_input(
        "L1 regularization - Helps with feature selection (recommended: 0)",
        default_config['lightgbm']['reg_alpha'], 0, 10)

    config['lightgbm']['reg_lambda'] = get_float_input(
        "L2 regularization - Helps prevent overfitting (recommended: 0)",
        default_config['lightgbm']['reg_lambda'], 0, 10)

    config['lightgbm']['min_child_samples'] = get_int_input(
        "Min samples in leaf - Higher values prevent overfitting (recommended: 20)",
        default_config['lightgbm']['min_child_samples'], 5, 100)

    # Fixed parameters from reference file (essential for performance)
    config['lightgbm']['device'] = default_config['lightgbm']['device']
    config['lightgbm']['random_state'] = default_config['lightgbm']['random_state']
    
    # CatBoost configuration with enhanced options and recommendations
    print(f"\n--- CatBoost Configuration ---")
    print("CatBoost has excellent default parameters and handles categorical features automatically.")
    config['catboost'] = {}

    # Optional tunable parameters
    print(f"\n--- CatBoost Advanced Parameters (Optional) ---")
    print("CatBoost works very well with defaults. Press Enter to use recommended values:")

    config['catboost']['iterations'] = get_int_input(
        "Number of iterations (trees) - More iterations = better fit (recommended: 1000)",
        default_config['catboost']['iterations'], 100, 5000)

    # Handle learning_rate specially (None means auto-determined)
    lr_input = input(f"Learning rate - Leave empty for auto-determination (recommended), or enter 0.01-0.3: ").strip()
    if lr_input:
        try:
            lr_value = float(lr_input)
            if 0.01 <= lr_value <= 0.3:
                config['catboost']['learning_rate'] = lr_value
            else:
                print(f"Value out of range, using auto-determination")
                config['catboost']['learning_rate'] = default_config['catboost']['learning_rate']
        except ValueError:
            print(f"Invalid input, using auto-determination")
            config['catboost']['learning_rate'] = default_config['catboost']['learning_rate']
    else:
        config['catboost']['learning_rate'] = default_config['catboost']['learning_rate']

    config['catboost']['depth'] = get_int_input(
        "Tree depth - Deeper trees capture more patterns but may overfit (recommended: 6)",
        default_config['catboost']['depth'], 3, 12)

    config['catboost']['l2_leaf_reg'] = get_float_input(
        "L2 regularization - Higher values prevent overfitting (recommended: 3)",
        default_config['catboost']['l2_leaf_reg'], 1, 10)

    config['catboost']['subsample'] = get_float_input(
        "Subsample ratio - Fraction of samples used per tree (recommended: 1.0)",
        default_config['catboost']['subsample'], 0.5, 1.0)

    config['catboost']['colsample_bylevel'] = get_float_input(
        "Column sampling per level - Fraction of features per tree level (recommended: 1.0)",
        default_config['catboost']['colsample_bylevel'], 0.5, 1.0)

    config['catboost']['random_strength'] = get_float_input(
        "Random strength - Amount of randomness for robust training (recommended: 1)",
        default_config['catboost']['random_strength'], 0, 10)

    # Fixed parameters from reference file (essential for performance)
    config['catboost']['task_type'] = default_config['catboost']['task_type']
    config['catboost']['early_stopping_rounds'] = default_config['catboost']['early_stopping_rounds']
    config['catboost']['verbose'] = default_config['catboost']['verbose']
    config['catboost']['random_state'] = default_config['catboost']['random_state']
    
    print(f"\n✓ Hyperparameter configuration completed!")

    # Display final configuration summary with explanations
    print(f"\n{'='*80}")
    print("FINAL HYPERPARAMETER CONFIGURATION SUMMARY")
    print('='*80)

    for model_name, params in config.items():
        print(f"\n--- {model_name.upper()} ---")
        for param, value in params.items():
            explanation = get_parameter_explanation(model_name, param, value)
            print(f"  {param}: {value} - {explanation}")

    print('='*80)
    return config

def get_parameter_explanation(model_name, param, value):
    """Get explanation for each parameter value."""
    explanations = {
        'xgboost': {
            'n_estimators': f"Number of trees to build",
            'learning_rate': f"Step size for gradient descent",
            'max_depth': f"Maximum tree depth (controls complexity)",
            'subsample': f"Fraction of samples used per tree",
            'colsample_bytree': f"Fraction of features used per tree",
            'reg_alpha': f"L1 regularization strength",
            'reg_lambda': f"L2 regularization strength",
            'gamma': f"Minimum loss reduction for splits",
            'tree_method': f"GPU-accelerated histogram method (FIXED)",
            'early_stopping_rounds': f"Stop if no improvement for 100 rounds (FIXED)",
            'random_state': f"Ensures reproducible results (FIXED)"
        },
        'lightgbm': {
            'n_estimators': f"Number of trees to build",
            'learning_rate': f"Step size for gradient descent",
            'max_depth': f"Maximum tree depth (-1 = no limit)",
            'subsample': f"Fraction of samples used per tree",
            'colsample_bytree': f"Fraction of features used per tree",
            'reg_alpha': f"L1 regularization strength",
            'reg_lambda': f"L2 regularization strength",
            'min_child_samples': f"Minimum samples required in leaf",
            'device': f"GPU acceleration enabled (FIXED)",
            'random_state': f"Ensures reproducible results (FIXED)"
        },
        'catboost': {
            'iterations': f"Number of trees to build",
            'learning_rate': f"Auto-determined optimal rate" if value is None else f"Step size for gradient descent",
            'depth': f"Maximum tree depth",
            'l2_leaf_reg': f"L2 regularization strength",
            'subsample': f"Fraction of samples used per tree",
            'colsample_bylevel': f"Fraction of features per tree level",
            'random_strength': f"Amount of randomness for robustness",
            'task_type': f"GPU acceleration enabled (FIXED)",
            'early_stopping_rounds': f"Stop if no improvement for 100 rounds (FIXED)",
            'verbose': f"Silent output for clean logs (FIXED)",
            'random_state': f"Ensures reproducible results (FIXED)"
        }
    }

    return explanations.get(model_name, {}).get(param, "Parameter configuration")

def get_int_input(prompt, default, min_val, max_val):
    """Get integer input with validation."""
    while True:
        try:
            user_input = input(f"{prompt} (default: {default}, range: {min_val}-{max_val}): ").strip()
            if not user_input:
                return default
            value = int(user_input)
            if min_val <= value <= max_val:
                return value
            else:
                print(f"Value must be between {min_val} and {max_val}")
        except ValueError:
            print("Please enter a valid integer")

def get_float_input(prompt, default, min_val, max_val):
    """Get float input with validation."""
    while True:
        try:
            user_input = input(f"{prompt} (default: {default}, range: {min_val}-{max_val}): ").strip()
            if not user_input:
                return default
            value = float(user_input)
            if min_val <= value <= max_val:
                return value
            else:
                print(f"Value must be between {min_val} and {max_val}")
        except ValueError:
            print("Please enter a valid number")

# Enhanced well separation system
def configure_well_separation(well_names, auto_mode=False):
    """Configure wells for training vs prediction."""
    print(f"\n{'='*60}")
    print("WELL SEPARATION CONFIGURATION")
    print('='*60)
    print("Configure which wells to use for training vs prediction:")
    print("- Training wells: Wells with complete log data used for model training")
    print("- Prediction wells: Wells with missing log curves where predictions will be applied")
    print("- Mixed wells: Wells used for both training (complete data) and prediction (missing data)")
    
    if auto_mode:
        # Auto mode: use first half for training, second half for prediction
        mid_point = len(well_names) // 2
        training_wells = well_names[:mid_point] if mid_point > 0 else well_names[:1]
        prediction_wells = well_names[mid_point:] if mid_point < len(well_names) else well_names[1:]
        mixed_wells = []
        
        print(f"Auto-mode configuration:")
        print(f"  Training wells: {training_wells}")
        print(f"  Prediction wells: {prediction_wells}")
        print(f"  Mixed wells: {mixed_wells}")
        
        return {
            'training_wells': training_wells,
            'prediction_wells': prediction_wells,
            'mixed_wells': mixed_wells,
            'mode': 'separated'
        }
    
    print(f"\nAvailable wells: {well_names}")
    print("\nChoose well separation mode:")
    print("  1. Separated mode - Designate specific wells for training vs prediction")
    print("  2. Mixed mode - Use all wells for both training and prediction (traditional approach)")
    
    while True:
        try:
            mode_input = input("Select mode (1-2, default: 2): ").strip()
            if not mode_input:
                separation_mode = 2
                break
            else:
                separation_mode = int(mode_input)
                if separation_mode in [1, 2]:
                    break
                else:
                    print("Invalid selection. Please enter 1 or 2.")
        except ValueError:
            print("Invalid input. Please enter 1 or 2.")
    
    if separation_mode == 2:
        # Mixed mode - traditional approach
        return {
            'training_wells': well_names,
            'prediction_wells': well_names,
            'mixed_wells': well_names,
            'mode': 'mixed'
        }
    
    # Separated mode
    print(f"\n--- Training Wells Selection ---")
    print("Select wells with complete log data for model training:")
    training_wells = console_select(well_names, "Select Training Wells:", 
                                  default=well_names[:len(well_names)//2], 
                                  multiple=True, auto_mode=False)
    
    remaining_wells = [w for w in well_names if w not in training_wells]
    if remaining_wells:
        print(f"\n--- Prediction Wells Selection ---")
        print("Select wells where predictions will be applied:")
        prediction_wells = console_select(remaining_wells, "Select Prediction Wells:", 
                                        default=remaining_wells, 
                                        multiple=True, auto_mode=False)
    else:
        prediction_wells = []
    
    # Option for mixed wells (wells used for both training and prediction)
    if training_wells and prediction_wells:
        print(f"\n--- Mixed Wells Selection (Optional) ---")
        print("Select wells to use for both training and prediction (optional):")
        all_wells_for_mixed = [w for w in well_names if w not in training_wells and w not in prediction_wells]
        if all_wells_for_mixed:
            mixed_wells = console_select(all_wells_for_mixed + ["None"], 
                                       "Select Mixed Wells (or 'None'):", 
                                       default=["None"], multiple=True, auto_mode=False)
            if "None" in mixed_wells:
                mixed_wells = []
        else:
            mixed_wells = []
    else:
        mixed_wells = []
    
    print(f"\n✓ Well separation configured:")
    print(f"  Training wells: {training_wells}")
    print(f"  Prediction wells: {prediction_wells}")
    print(f"  Mixed wells: {mixed_wells}")
    
    return {
        'training_wells': training_wells,
        'prediction_wells': prediction_wells,
        'mixed_wells': mixed_wells,
        'mode': 'separated'
    }

# Consolidated selection function (from original)
def console_select(options, prompt, default=None, multiple=False, max_selections=None, auto_mode=False):
    """Unified console selection function for both single and multiple selections."""
    if not options:
        print("No options available.")
        return [] if multiple else None
    
    print(f"\n{prompt}")
    for i, option in enumerate(options, 1):
        print(f"  {i}. {option}")
    
    # Handle defaults
    if default is not None:
        if multiple and isinstance(default, list):
            default_indices = [options.index(d) + 1 for d in default if d in options]
        elif not multiple and default in options:
            default_indices = [options.index(default) + 1]
        else:
            default_indices = [default] if isinstance(default, int) else []
        
        if default_indices:
            print(f"Default: {', '.join([str(i) for i in default_indices])}")
    
    # Auto mode
    if auto_mode:
        if default_indices:
            selected = default_indices
        else:
            max_select = min(max_selections or 5, len(options)) if multiple else 1
            selected = list(range(1, max_select + 1)) if multiple else [1]
        
        result = [options[i-1] for i in selected]
        print(f"Auto-selected: {', '.join(result) if multiple else result[0]}")
        return result if multiple else result[0]
    
    # User input
    print("Enter selection(s) - numbers separated by commas" + (" or 'all'" if multiple else "") + ", or Enter for default:")
    
    while True:
        try:
            user_input = input("Selection: ").strip()
            
            if not user_input and default_indices:
                selected = default_indices
                break
            elif user_input.lower() == 'all' and multiple:
                if max_selections and len(options) > max_selections:
                    print(f"Cannot select all - max {max_selections} allowed.")
                    continue
                selected = list(range(1, len(options) + 1))
                break
            elif user_input:
                if multiple:
                    selected = [int(x.strip()) for x in user_input.split(',')]
                    if max_selections and len(selected) > max_selections:
                        print(f"Too many selections. Max {max_selections} allowed.")
                        continue
                else:
                    selected = [int(user_input)]
                
                if any(i < 1 or i > len(options) for i in selected):
                    print(f"Invalid selection. Enter numbers 1-{len(options)}")
                    continue
                break
            else:
                print("Please enter a selection.")
        except (ValueError, EOFError, KeyboardInterrupt):
            if default_indices:
                selected = default_indices
                break
            print("Invalid input. Try again.")
    
    result = [options[i-1] for i in selected]
    print(f"Selected: {', '.join(result) if multiple else result[0]}")
    return result if multiple else result[0]

# Data loading and selection
print('Setting up interactive selection...')

# Get available logs and wells
try:
    available_logs = {log.petrel_name: log for log in petrel.global_well_logs if hasattr(log, 'petrel_name')}
    log_names = sorted(available_logs.keys())
    print(f'Found {len(log_names)} global well logs')

    # Handle wells with proper error handling (from working version)
    try:
        wells = list(petrel.wells)
        well_names = [w.petrel_name for w in wells]
        print(f'Found {len(wells)} wells')
    except Exception as e:
        print(f'ERROR accessing wells: {str(e)}')
        print(f'Type of petrel.wells: {type(petrel.wells)}')

        # Fallback: try manual iteration approach
        print('Trying fallback approach...')
        wells = []
        well_names = []
        try:
            for well in petrel.wells:
                if hasattr(well, 'petrel_name'):
                    wells.append(well)
                    well_names.append(well.petrel_name)
            print(f'Fallback found {len(wells)} wells')
        except Exception as e2:
            print(f'Fallback also failed: {str(e2)}')
            wells = []
            well_names = []

    if not wells or not log_names:
        print('ERROR: No wells or logs found in the project!')
        print('Please ensure:')
        print('1. Petrel project is open and contains wells/logs')
        print('2. Wells and logs are properly loaded in the project')
        print('3. Python Tool Pro connection is working correctly')
        raise ValueError("No wells or logs available for selection")

except Exception as e:
    print(f'ERROR: {str(e)}')
    raise

# Enhanced configuration system
print(f"\n{'='*80}")
print("ENHANCED LOG IMPUTATION CONFIGURATION")
print('='*80)

# Step 1: Configure hyperparameters
hyperparameter_config = get_hyperparameter_config(auto_mode=AUTO_MODE)

# Step 2: Configure well separation
well_separation_config = configure_well_separation(well_names, auto_mode=AUTO_MODE)

# Step 3: Interactive selections for logs
default_input_logs = ['GR', 'RHOB', 'NPHI', 'Vp'] if all(log in log_names for log in ['GR', 'RHOB', 'NPHI', 'Vp']) else log_names[:4]
selected_input_logs = console_select(log_names, "Select Input Logs for ML Training:",
                                   default=default_input_logs, multiple=True, auto_mode=AUTO_MODE)

default_target = 'Vs' if 'Vs' in log_names else log_names[0]
selected_target_log = console_select(log_names, "Select Target Log for Imputation:",
                                   default=default_target, auto_mode=AUTO_MODE)

# Step 4: Prediction mode selection with consolidated display
print(f"\n{'='*60}")
print("PREDICTION MODE SELECTION")
print('='*60)
print("Available prediction modes:")
print("  1. Fill Missing Values Only - Uses ML to predict only missing data points")
print("  2. Cross-Validation Test - Validates model performance using held-out data")
print("  3. Re-predict Entire Log - Replaces entire log with ML predictions")

# Create a simple selection without duplicating the numbered list
print("\nSelect Prediction Mode:")
if not AUTO_MODE:
    print("Default: 1")
    print("Enter selection (1-3) or press Enter for default:")

    while True:
        try:
            user_input = input("Selection: ").strip()
            if not user_input:
                selected_mode = 1
                break
            else:
                selected_mode = int(user_input)
                if selected_mode in [1, 2, 3]:
                    break
                else:
                    print("Invalid selection. Please enter 1, 2, or 3.")
        except (ValueError, EOFError, KeyboardInterrupt):
            print("Invalid input. Using default selection...")
            selected_mode = 1
            break
else:
    selected_mode = 1
    print("Auto-selected: 1")

print(f"Selected: {selected_mode}. {PREDICTION_MODES[selected_mode]}")
prediction_mode = int(selected_mode)
prediction_mode_name = PREDICTION_MODES[prediction_mode]

# Determine which wells to use based on separation configuration
if well_separation_config['mode'] == 'separated':
    # Use training wells + mixed wells for training data
    training_well_names = well_separation_config['training_wells'] + well_separation_config['mixed_wells']
    # Use prediction wells + mixed wells for prediction
    prediction_well_names = well_separation_config['prediction_wells'] + well_separation_config['mixed_wells']

    print(f'\n✓ Well separation mode: SEPARATED')
    print(f'✓ Training wells ({len(training_well_names)}): {training_well_names}')
    print(f'✓ Prediction wells ({len(prediction_well_names)}): {prediction_well_names}')
else:
    # Traditional mixed mode
    training_well_names = well_separation_config['mixed_wells']
    prediction_well_names = well_separation_config['mixed_wells']

    print(f'\n✓ Well separation mode: MIXED (traditional)')
    print(f'✓ Wells for training and prediction ({len(training_well_names)}): {training_well_names}')

print(f'✓ Input logs: {selected_input_logs}')
print(f'✓ Target log: {selected_target_log}')
print(f'✓ Prediction mode: {prediction_mode_name}')

# Get selected well objects
training_wells = [w for w in wells if w.petrel_name in training_well_names]
prediction_wells = [w for w in wells if w.petrel_name in prediction_well_names]
all_selected_wells = list(set(training_wells + prediction_wells))  # Remove duplicates

LOG_NAMES = selected_input_logs + [selected_target_log]
logs = [available_logs[name] for name in LOG_NAMES if name in available_logs]

# Enhanced data loading with well separation support
print(f'\nLoading data from wells...')
training_data = pd.DataFrame()
prediction_data = pd.DataFrame()

# Load training data
if training_wells:
    print(f'Loading training data from {len(training_wells)} wells...')
    for i, w in enumerate(training_wells):
        print(f'Processing training well {i+1}/{len(training_wells)}: {w.petrel_name}')
        try:
            df = w.logs_dataframe(logs)
            if not df.empty:
                df['WELL'] = w.petrel_name
                df['WELL_TYPE'] = 'TRAINING'
                training_data = pd.concat([training_data, df], ignore_index=False)
                print(f'  ✓ Loaded {len(df)} samples')
        except Exception as e:
            print(f'  ✗ Error: {str(e)}')

# Load prediction data (may overlap with training data in mixed mode)
if prediction_wells:
    print(f'Loading prediction data from {len(prediction_wells)} wells...')
    for i, w in enumerate(prediction_wells):
        print(f'Processing prediction well {i+1}/{len(prediction_wells)}: {w.petrel_name}')
        try:
            df = w.logs_dataframe(logs)
            if not df.empty:
                df['WELL'] = w.petrel_name
                df['WELL_TYPE'] = 'PREDICTION'
                prediction_data = pd.concat([prediction_data, df], ignore_index=False)
                print(f'  ✓ Loaded {len(df)} samples')
        except Exception as e:
            print(f'  ✗ Error: {str(e)}')

# Combine data for processing
if well_separation_config['mode'] == 'separated':
    # In separated mode, combine training and prediction data
    well_data = pd.concat([training_data, prediction_data], ignore_index=False)
    print(f'\n✓ Combined data shape: {well_data.shape}')
    print(f'✓ Training data: {len(training_data)} samples from {len(training_wells)} wells')
    print(f'✓ Prediction data: {len(prediction_data)} samples from {len(prediction_wells)} wells')
else:
    # In mixed mode, use all data for both training and prediction
    well_data = training_data.copy()
    well_data['WELL_TYPE'] = 'MIXED'
    print(f'\n✓ Combined data shape: {well_data.shape}')
    print(f'✓ Mixed mode: {len(well_data)} samples from {len(training_wells)} wells')

if well_data.empty:
    raise ValueError("No data loaded!")

well_data.reset_index(drop=False, inplace=True)

# Basic cleaning
for col, (min_val, max_val) in [('GR', (0, 300)), ('NPHI', (0, 1)), ('RHOB', (1.0, 3.5))]:
    if col in well_data.columns:
        well_data[col] = np.where((well_data[col] >= min_val) & (well_data[col] <= max_val),
                                 well_data[col], np.nan)
        print(f'Cleaned {col}')

# Enhanced QC Data Coverage Analysis
def generate_qc_report(well_data, log_names, training_wells, prediction_wells, well_separation_config):
    """Generate comprehensive QC report for data coverage and training readiness."""
    print(f'\n{"="*80}')
    print('QUALITY CONTROL (QC) DATA COVERAGE REPORT')
    print('='*80)

    qc_report = {
        'overall_coverage': {},
        'well_coverage': {},
        'training_readiness': {},
        'warnings': [],
        'errors': []
    }

    # Overall coverage analysis
    log_columns = [col for col in log_names if col in well_data.columns]
    overall_coverage = 1.0 - well_data[log_columns].isna().mean()
    qc_report['overall_coverage'] = overall_coverage.to_dict()

    print('\n1. OVERALL DATA COVERAGE:')
    print('-' * 40)
    for log_name, cov in overall_coverage.items():
        status = "✓ GOOD" if cov >= 0.7 else "⚠ MODERATE" if cov >= 0.5 else "✗ POOR"
        print(f'  {log_name}: {cov:.2%} {status}')
        if cov < 0.5:
            qc_report['warnings'].append(f'Low overall coverage for {log_name}: {cov:.2%}')

    # Per-well coverage analysis
    print('\n2. PER-WELL DATA COVERAGE:')
    print('-' * 40)

    well_names = well_data['WELL'].unique()
    for well_name in well_names:
        well_subset = well_data[well_data['WELL'] == well_name]
        well_coverage = 1.0 - well_subset[log_columns].isna().mean()
        qc_report['well_coverage'][well_name] = well_coverage.to_dict()

        # Determine well type
        well_type = 'TRAINING' if well_name in training_wells else 'PREDICTION' if well_name in prediction_wells else 'MIXED'

        print(f'\n  Well: {well_name} ({well_type})')
        print(f'  Samples: {len(well_subset)}')

        insufficient_logs = []
        for log_name, cov in well_coverage.items():
            status = "✓" if cov >= 0.7 else "⚠" if cov >= 0.5 else "✗"
            print(f'    {log_name}: {cov:.2%} {status}')
            if cov < 0.5:
                insufficient_logs.append(log_name)

        if insufficient_logs:
            warning_msg = f'Well {well_name} has insufficient data for logs: {", ".join(insufficient_logs)}'
            qc_report['warnings'].append(warning_msg)
            if well_type == 'TRAINING':
                qc_report['errors'].append(f'Training well {well_name} has insufficient data - may affect model quality')

    # Training readiness analysis
    print('\n3. TRAINING READINESS ANALYSIS:')
    print('-' * 40)

    if well_separation_config['mode'] == 'separated':
        # Check training wells specifically
        training_data = well_data[well_data['WELL'].isin(training_wells)]
        if training_data.empty:
            error_msg = 'No training data available from selected training wells'
            qc_report['errors'].append(error_msg)
            print(f'  ✗ ERROR: {error_msg}')
        else:
            training_coverage = 1.0 - training_data[log_columns].isna().mean()
            qc_report['training_readiness']['training_coverage'] = training_coverage.to_dict()

            print(f'  Training wells: {len(training_wells)}')
            print(f'  Training samples: {len(training_data)}')
            print('  Training data coverage:')

            for log_name, cov in training_coverage.items():
                status = "✓ READY" if cov >= 0.7 else "⚠ MARGINAL" if cov >= 0.5 else "✗ INSUFFICIENT"
                print(f'    {log_name}: {cov:.2%} {status}')
                if cov < 0.5:
                    error_msg = f'Insufficient training data for {log_name}: {cov:.2%}'
                    qc_report['errors'].append(error_msg)

            # Check if target log has sufficient complete data for training
            target_log = log_names[-1]  # Assuming last log is target
            target_complete = training_data[target_log].notna().sum()
            target_total = len(training_data)
            target_completeness = target_complete / target_total if target_total > 0 else 0

            print(f'\n  Target log ({target_log}) training readiness:')
            print(f'    Complete samples: {target_complete}/{target_total} ({target_completeness:.2%})')

            if target_complete < 10:
                error_msg = f'Insufficient complete target data for training: only {target_complete} samples'
                qc_report['errors'].append(error_msg)
                print(f'    ✗ ERROR: {error_msg}')
            elif target_completeness < 0.3:
                warning_msg = f'Low target completeness in training data: {target_completeness:.2%}'
                qc_report['warnings'].append(warning_msg)
                print(f'    ⚠ WARNING: {warning_msg}')
            else:
                print(f'    ✓ READY: Sufficient training data available')

    else:
        # Mixed mode analysis
        print(f'  Mode: Mixed (all wells used for training and prediction)')
        print(f'  Total wells: {len(well_names)}')
        print(f'  Total samples: {len(well_data)}')

        target_log = log_names[-1]  # Assuming last log is target
        target_complete = well_data[target_log].notna().sum()
        target_total = len(well_data)
        target_completeness = target_complete / target_total if target_total > 0 else 0

        print(f'  Target log ({target_log}) completeness: {target_complete}/{target_total} ({target_completeness:.2%})')

        if target_complete < 10:
            error_msg = f'Insufficient complete target data: only {target_complete} samples'
            qc_report['errors'].append(error_msg)
        elif target_completeness < 0.3:
            warning_msg = f'Low target completeness: {target_completeness:.2%}'
            qc_report['warnings'].append(warning_msg)

    # Summary
    print('\n4. QC SUMMARY:')
    print('-' * 40)
    print(f'  Warnings: {len(qc_report["warnings"])}')
    print(f'  Errors: {len(qc_report["errors"])}')

    if qc_report['errors']:
        print('\n  CRITICAL ISSUES:')
        for error in qc_report['errors']:
            print(f'    ✗ {error}')

    if qc_report['warnings']:
        print('\n  WARNINGS:')
        for warning in qc_report['warnings']:
            print(f'    ⚠ {warning}')

    if not qc_report['errors'] and not qc_report['warnings']:
        print('  ✓ All QC checks passed - Ready for training!')
    elif qc_report['errors']:
        print('  ✗ Critical issues found - Training may fail or produce poor results')
    else:
        print('  ⚠ Some issues found - Training may proceed with caution')

    print('='*80)
    return qc_report

# Validate well selection for training
def validate_training_wells(well_data, training_wells, selected_input_logs, selected_target_log):
    """Validate that training wells have sufficient data for the selected logs."""
    print(f'\n{"="*60}')
    print('TRAINING WELLS VALIDATION')
    print('='*60)

    validation_results = {
        'valid_wells': [],
        'invalid_wells': [],
        'warnings': []
    }

    required_logs = selected_input_logs + [selected_target_log]

    for well_name in training_wells:
        well_subset = well_data[well_data['WELL'] == well_name]

        if well_subset.empty:
            validation_results['invalid_wells'].append({
                'well': well_name,
                'reason': 'No data found for this well'
            })
            print(f'✗ {well_name}: No data found')
            continue

        # Check each required log
        well_issues = []
        well_coverage = {}

        for log_name in required_logs:
            if log_name not in well_subset.columns:
                well_issues.append(f'{log_name} not available')
            else:
                coverage = 1.0 - well_subset[log_name].isna().mean()
                well_coverage[log_name] = coverage
                if coverage < 0.3:
                    well_issues.append(f'{log_name} coverage too low ({coverage:.1%})')
                elif coverage < 0.5:
                    validation_results['warnings'].append(f'{well_name}: {log_name} has moderate coverage ({coverage:.1%})')

        # Check target log specifically for training
        if selected_target_log in well_subset.columns:
            target_complete = well_subset[selected_target_log].notna().sum()
            if target_complete < 5:
                well_issues.append(f'Insufficient target data ({target_complete} complete samples)')

        if well_issues:
            validation_results['invalid_wells'].append({
                'well': well_name,
                'reason': '; '.join(well_issues),
                'coverage': well_coverage
            })
            print(f'✗ {well_name}: {"; ".join(well_issues)}')
        else:
            validation_results['valid_wells'].append({
                'well': well_name,
                'coverage': well_coverage,
                'samples': len(well_subset)
            })
            coverage_summary = ', '.join([f'{log}:{cov:.1%}' for log, cov in well_coverage.items()])
            print(f'✓ {well_name}: Ready ({coverage_summary})')

    print(f'\nValidation Summary:')
    print(f'  Valid training wells: {len(validation_results["valid_wells"])}/{len(training_wells)}')
    print(f'  Invalid training wells: {len(validation_results["invalid_wells"])}')
    print(f'  Warnings: {len(validation_results["warnings"])}')

    if validation_results['invalid_wells']:
        print(f'\n⚠ Invalid wells will be excluded from training')

    print('='*60)
    return validation_results

# Validate training wells
if well_separation_config['mode'] == 'separated':
    training_validation = validate_training_wells(well_data, training_well_names, selected_input_logs, selected_target_log)
else:
    print('\nSkipping training well validation (mixed mode - all wells used for training)')
    training_validation = None

# Generate QC report
qc_report = generate_qc_report(well_data, LOG_NAMES, training_well_names, prediction_well_names, well_separation_config)

# Coverage analysis (simplified after QC report)
log_columns = [col for col in LOG_NAMES if col in well_data.columns]
coverage = 1.0 - well_data[log_columns].isna().mean()

# Visualize coverage with matplotlib
try:
    import matplotlib.pyplot as plt

    # Create coverage plot with matplotlib
    fig, ax = plt.subplots(1, 1, figsize=(10, 6))
    bars = ax.bar(coverage.index, coverage.values, alpha=0.7, color='skyblue', edgecolor='black')
    ax.set_xlabel('Log Names')
    ax.set_ylabel('Coverage')
    ax.set_title('Data Coverage for Selected Logs')
    ax.grid(True, alpha=0.3)

    # Add value labels on bars
    for i, (log_name, cov) in enumerate(coverage.items()):
        ax.text(i, cov + 0.01, f'{cov:.2%}', ha='center', va='bottom', fontweight='bold')

    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.show()

except ImportError:
    # Fallback to plotly if matplotlib not available
    fig = px.bar(coverage, labels={'value':'Coverage'}, title='Data coverage for selected logs')
    fig.show()

# Enhanced ML imputation function with comprehensive error handling and QC
def impute_logs_enhanced(df, depth_col, feature_cols, targets, hyperparams, well_config, prediction_mode=1, test_percentage=25.0):
    """Enhanced ML imputation with configurable hyperparameters, well separation, and comprehensive error handling."""
    res = df.copy()
    model_results = {}
    training_errors = []
    training_warnings = []

    # Create ML models with user-configured hyperparameters
    boosters = [
        ('XGBoost', XGBRegressor(**hyperparams['xgboost'])),
        ('LightGBM', LGBMRegressor(**hyperparams['lightgbm'])),
        ('CatBoost', CatBoostRegressor(**hyperparams['catboost']))
    ]

    feature_set = feature_cols + [depth_col]

    for tgt in targets:
        print(f'--- Processing {tgt} (Mode: {PREDICTION_MODES[prediction_mode]}) ---')
        print(f'Well separation mode: {well_config["mode"].upper()}')

        # Prepare training data based on well separation and prediction mode
        if well_config['mode'] == 'separated':
            # In separated mode, use only training wells for model training
            training_mask = res['WELL_TYPE'] == 'TRAINING'
            training_subset = res[training_mask]

            if prediction_mode == 1:  # Fill missing only
                train = training_subset[training_subset[tgt].notna()][feature_set + [tgt]].copy()
            elif prediction_mode == 2:  # Cross-validation
                complete_data = training_subset[training_subset[tgt].notna()][feature_set + [tgt]].copy()
                if len(complete_data) == 0:
                    print('No complete training data available')
                    continue
                train, test_data = train_test_split(complete_data, test_size=test_percentage/100, random_state=42)
            else:  # Re-predict entire log
                train = training_subset[training_subset[tgt].notna()][feature_set + [tgt]].copy()

            print(f'Using {len(train)} samples from training wells for model training')
        else:
            # Traditional mixed mode
            if prediction_mode == 1:  # Fill missing only
                train = res[res[tgt].notna()][feature_set + [tgt]].copy()
            elif prediction_mode == 2:  # Cross-validation
                complete_data = res[res[tgt].notna()][feature_set + [tgt]].copy()
                if len(complete_data) == 0:
                    continue
                train, test_data = train_test_split(complete_data, test_size=test_percentage/100, random_state=42)
            else:  # Re-predict entire log
                train = res[res[tgt].notna()][feature_set + [tgt]].copy()

            print(f'Using {len(train)} samples from all wells for model training')

        if train.empty:
            error_msg = f'No training data available for {tgt}'
            print(f'✗ ERROR: {error_msg}')
            training_errors.append(error_msg)
            continue

        # Validate training data quality
        if len(train) < 10:
            error_msg = f'Insufficient training samples for {tgt}: only {len(train)} samples (minimum 10 required)'
            print(f'✗ ERROR: {error_msg}')
            training_errors.append(error_msg)
            continue
        elif len(train) < 50:
            warning_msg = f'Limited training samples for {tgt}: only {len(train)} samples (recommended: 50+)'
            print(f'⚠ WARNING: {warning_msg}')
            training_warnings.append(warning_msg)

        X = train.drop(columns=[tgt]).apply(lambda c: c.fillna(c.mean()), axis=0)
        y = train[tgt]
        Xtr, Xval, ytr, yval = train_test_split(X, y, test_size=0.25, random_state=42)

        # Train models and find best and second-best
        trained_models = {}
        model_performances = {}
        best_model, best_name, best_mae = None, None, float("inf")

        print(f'Training {len(boosters)} models with custom hyperparameters...')
        failed_models = []
        for name, model in boosters:
            try:
                print(f'  Training {name}...', end=' ')
                model.fit(Xtr, ytr)
                mae = mean_absolute_error(yval, model.predict(Xval))
                print(f'MAE = {mae:.3f} ✓')
                trained_models[name] = model
                model_performances[name] = mae
                if mae < best_mae:
                    best_model, best_name, best_mae = model, name, mae
            except Exception as e:
                error_msg = f'{name} training failed for {tgt}: {str(e)}'
                print(f'FAILED ✗')
                print(f'    Error: {str(e)}')
                training_errors.append(error_msg)
                failed_models.append(name)

        if best_model is None:
            error_msg = f'All models failed to train for {tgt}. Failed models: {", ".join(failed_models)}'
            print(f'✗ CRITICAL ERROR: {error_msg}')
            training_errors.append(error_msg)
            continue

        # Check if too many models failed
        if len(failed_models) >= len(boosters) - 1:
            warning_msg = f'Most models failed for {tgt}. Only {best_name} succeeded. Results may be unreliable.'
            print(f'⚠ WARNING: {warning_msg}')
            training_warnings.append(warning_msg)

        # Find second-best model
        sorted_models = sorted(model_performances.items(), key=lambda x: x[1])
        second_best_name, second_best_mae = sorted_models[1] if len(sorted_models) > 1 else (None, None)
        second_best_model = trained_models.get(second_best_name) if second_best_name else None

        print(f'✓ Best model: {best_name} (MAE={best_mae:.3f})')
        if second_best_name:
            print(f'✓ Second-best model: {second_best_name} (MAE={second_best_mae:.3f})')

        # Generate predictions for the appropriate wells
        if well_config['mode'] == 'separated':
            # Apply predictions to prediction wells and mixed wells
            prediction_mask = (res['WELL_TYPE'] == 'PREDICTION') | (res['WELL_TYPE'] == 'MIXED')
            X_pred = res[prediction_mask][feature_set].apply(lambda c: c.fillna(c.mean()), axis=0)

            if not X_pred.empty:
                best_preds = best_model.predict(X_pred)
                best_preds_series = pd.Series(index=res[prediction_mask].index, data=best_preds)

                # Generate second-best predictions if available
                second_best_preds_series = None
                if second_best_model is not None:
                    second_best_preds = second_best_model.predict(X_pred)
                    second_best_preds_series = pd.Series(index=res[prediction_mask].index, data=second_best_preds)

                # Initialize full prediction series
                full_best_preds = pd.Series(index=res.index, dtype=float)
                full_best_preds[prediction_mask] = best_preds_series

                full_second_best_preds = None
                if second_best_preds_series is not None:
                    full_second_best_preds = pd.Series(index=res.index, dtype=float)
                    full_second_best_preds[prediction_mask] = second_best_preds_series

                print(f'Applied predictions to {len(X_pred)} samples in prediction wells')
            else:
                print('No prediction wells data available')
                continue
        else:
            # Traditional mode: predict for all data
            X_full = res[feature_set].apply(lambda c: c.fillna(c.mean()), axis=0)
            best_preds = best_model.predict(X_full)
            full_best_preds = pd.Series(best_preds, index=res.index)

            # Generate second-best predictions if available
            full_second_best_preds = None
            if second_best_model is not None:
                second_best_preds = second_best_model.predict(X_full)
                full_second_best_preds = pd.Series(second_best_preds, index=res.index)

        # Apply prediction mode logic
        if prediction_mode == 1:
            res[f'{tgt}_pred'] = full_best_preds
            res[f'{tgt}_imputed'] = res[tgt].fillna(full_best_preds)
            missing_count = res[tgt].isna().sum()
            print(f'Imputed {missing_count}/{len(res)} missing values ({missing_count/len(res):.1%})')
        elif prediction_mode == 2:
            res[f'{tgt}_pred'] = full_best_preds
            res[f'{tgt}_imputed'] = res[tgt].fillna(full_best_preds)
            if 'test_data' in locals():
                X_test = test_data.drop(columns=[tgt]).apply(lambda c: c.fillna(c.mean()), axis=0)
                test_preds = best_model.predict(X_test)
                test_mae = mean_absolute_error(test_data[tgt], test_preds)
                print(f'Cross-validation MAE: {test_mae:.3f}')
        else:  # Re-predict entire log
            res[f'{tgt}_pred'] = full_best_preds
            res[f'{tgt}_imputed'] = full_best_preds
            print(f'Re-predicted entire log ({len(res)} samples)')

        # Calculate errors where original data exists
        original_mask = res[tgt].notna()
        if original_mask.any():
            res[f'{tgt}_error'] = np.nan
            res.loc[original_mask, f'{tgt}_error'] = np.abs(res.loc[original_mask, tgt] - full_best_preds.loc[original_mask]) / res.loc[original_mask, tgt] * 100

        # Store results
        model_results[tgt] = {
            'trained_models': trained_models,
            'model_performances': model_performances,
            'best_model_name': best_name,
            'best_mae': best_mae,
            'best_predictions': full_best_preds,
            'second_best_model_name': second_best_name,
            'second_best_mae': second_best_mae,
            'second_best_predictions': full_second_best_preds,
            'original_data': res[tgt].copy(),
            'prediction_mode': prediction_mode,
            'hyperparameters': hyperparams,
            'well_separation': well_config,
            'training_errors': training_errors,
            'training_warnings': training_warnings
        }

    # Generate training summary report
    print(f'\n{"="*80}')
    print('TRAINING SUMMARY REPORT')
    print('='*80)

    if training_errors:
        print(f'\n✗ TRAINING ERRORS ({len(training_errors)}):')
        for i, error in enumerate(training_errors, 1):
            print(f'  {i}. {error}')

    if training_warnings:
        print(f'\n⚠ TRAINING WARNINGS ({len(training_warnings)}):')
        for i, warning in enumerate(training_warnings, 1):
            print(f'  {i}. {warning}')

    if not training_errors and not training_warnings:
        print('\n✓ All training completed successfully with no issues!')
    elif training_errors:
        print(f'\n⚠ Training completed with {len(training_errors)} errors and {len(training_warnings)} warnings.')
        print('  Some targets may not have been processed. Check individual target results.')
    else:
        print(f'\n✓ Training completed with {len(training_warnings)} warnings but no critical errors.')

    print('='*80)

    return res, model_results

# Run enhanced imputation
DEPTH_COL = 'MD'
print(f'\n{"="*60}')
print('RUNNING ENHANCED ML IMPUTATION')
print(f'{"="*60}')
print(f'Target: {selected_target_log}, Features: {selected_input_logs}, Mode: {prediction_mode_name}')
print(f'Well separation: {well_separation_config["mode"].upper()}')
print(f'Hyperparameters configured: {list(hyperparameter_config.keys())}')

results, model_results = impute_logs_enhanced(well_data, DEPTH_COL, selected_input_logs,
                                            [selected_target_log], hyperparameter_config,
                                            well_separation_config, prediction_mode, 25.0)

print(f'\n✓ Processing completed! Results shape: {results.shape}')
new_columns = [col for col in results.columns if any(suffix in col for suffix in ['_pred', '_imputed', '_error'])]
print(f'New columns: {new_columns}')

# Enhanced visualization function with well separation awareness
def create_enhanced_visualization(results_df, model_results, target_log, well_config):
    """Enhanced visualization with well separation awareness."""
    print(f'\n{"="*60}')
    print('GENERATING ENHANCED VISUALIZATION')
    print("="*60)

    if target_log not in model_results:
        print(f'No model results for {target_log}')
        return

    try:
        import matplotlib.pyplot as plt

        target_data = model_results[target_log]
        original_data = target_data['original_data']
        best_preds = target_data['best_predictions']
        best_model_name = target_data['best_model_name']
        hyperparams = target_data['hyperparameters']

        # Get unique wells and their types
        if well_config['mode'] == 'separated':
            training_wells = well_config['training_wells']
            prediction_wells = well_config['prediction_wells']
            mixed_wells = well_config['mixed_wells']
            all_wells = list(set(training_wells + prediction_wells + mixed_wells))
        else:
            all_wells = well_config['mixed_wells']
            training_wells = all_wells
            prediction_wells = all_wells
            mixed_wells = all_wells

        # Limit wells for visualization clarity
        max_wells_to_plot = min(6, len(all_wells))
        wells_to_plot = all_wells[:max_wells_to_plot]

        # Create individual well plots in horizontal layout
        n_wells = len(wells_to_plot)
        fig, axes = plt.subplots(1, n_wells, figsize=(5 * n_wells, 8))

        # Handle single well case
        if n_wells == 1:
            axes = [axes]

        # Define colors based on well type
        def get_well_color(well_name):
            if well_name in training_wells and well_name not in prediction_wells:
                return 'blue'  # Training only
            elif well_name in prediction_wells and well_name not in training_wells:
                return 'red'   # Prediction only
            else:
                return 'green' # Mixed

        # Plot individual wells
        for i, well_name in enumerate(wells_to_plot):
            ax = axes[i]
            well_mask = results_df['WELL'] == well_name
            well_data = results_df[well_mask]

            if well_data.empty:
                ax.text(0.5, 0.5, f'No data\nfor {well_name}', ha='center', va='center',
                       transform=ax.transAxes, fontsize=12)
                ax.set_title(f'{well_name}')
                continue

            well_md = well_data['MD']
            well_original = original_data[well_mask]
            well_preds = best_preds[well_mask]
            well_color = get_well_color(well_name)

            # Plot original data
            valid_original = ~well_original.isna()
            if valid_original.any():
                ax.plot(well_original[valid_original], well_md[valid_original],
                       'ko-', markersize=3, linewidth=2, alpha=0.8, label='Original Data')

            # Plot predictions
            valid_preds = ~well_preds.isna()
            if valid_preds.any():
                ax.plot(well_preds[valid_preds], well_md[valid_preds],
                       color=well_color, linestyle='-', linewidth=2, alpha=0.9,
                       label='ML Prediction')

            # Calculate and display metrics for this well
            if valid_original.any() and valid_preds.any():
                overlap_mask = valid_original & valid_preds
                if overlap_mask.any():
                    from sklearn.metrics import mean_absolute_error, r2_score
                    well_mae = mean_absolute_error(well_original[overlap_mask], well_preds[overlap_mask])
                    well_r2 = r2_score(well_original[overlap_mask], well_preds[overlap_mask])

                    # Add metrics text box
                    well_type = 'Training' if well_name in training_wells and well_name not in prediction_wells else \
                               'Prediction' if well_name in prediction_wells and well_name not in training_wells else 'Mixed'
                    metrics_text = f'Type: {well_type}\nMAE: {well_mae:.3f}\nR²: {well_r2:.3f}'
                    ax.text(0.02, 0.98, metrics_text, transform=ax.transAxes,
                           verticalalignment='top', fontsize=10,
                           bbox=dict(boxstyle='round,pad=0.3', facecolor='lightblue', alpha=0.8))

            # Formatting
            ax.set_ylabel('Measured Depth (MD)' if i == 0 else '')
            ax.set_xlabel(f'{target_log} Values')
            ax.set_title(f'{well_name}', fontweight='bold', fontsize=12)
            ax.grid(True, alpha=0.3)
            ax.invert_yaxis()

            # Add legend only to first plot
            if i == 0:
                ax.legend(loc='lower right', fontsize=10)

        # Adjust layout and add main title
        plt.tight_layout()
        title_text = f'Enhanced Well Analysis: {target_log} (Best Model: {best_model_name})\n'
        title_text += f'Well Mode: {well_config["mode"].upper()}'
        plt.suptitle(title_text, fontsize=14, fontweight='bold', y=0.95)
        plt.show()

        # Print enhanced summary with hyperparameters
        print(f'\n{"="*80}')
        print('ENHANCED SUMMARY STATISTICS')
        print('='*80)
        print(f'Best Model: {best_model_name} (MAE: {target_data["best_mae"]:.3f})')
        print(f'Well Separation Mode: {well_config["mode"].upper()}')

        if well_config['mode'] == 'separated':
            print(f'Training Wells: {training_wells}')
            print(f'Prediction Wells: {prediction_wells}')
            if mixed_wells:
                print(f'Mixed Wells: {mixed_wells}')

        print(f'\nHyperparameters Used:')
        for model_name, params in hyperparams.items():
            print(f'  {model_name.upper()}: {params}')

        print('='*80)

    except ImportError:
        print('⚠ Matplotlib not available. Showing text summary only.')
        print(f'Best Model: {target_data["best_model_name"]} (MAE: {target_data["best_mae"]:.3f})')
    except Exception as e:
        print(f'Error in visualization: {str(e)}')

# Generate comprehensive final QC report
def generate_final_qc_report(qc_report, training_validation, model_results, selected_target_log):
    """Generate final comprehensive QC report including all findings."""
    print(f'\n{"="*100}')
    print('COMPREHENSIVE QUALITY CONTROL FINAL REPORT')
    print('='*100)

    # Data Coverage Summary
    print('\n1. DATA COVERAGE SUMMARY:')
    print('-' * 50)
    if qc_report['errors']:
        print(f'   ✗ Critical data issues: {len(qc_report["errors"])}')
        for error in qc_report['errors'][:3]:  # Show first 3 errors
            print(f'     • {error}')
        if len(qc_report['errors']) > 3:
            print(f'     ... and {len(qc_report["errors"]) - 3} more')
    else:
        print('   ✓ No critical data coverage issues')

    if qc_report['warnings']:
        print(f'   ⚠ Data warnings: {len(qc_report["warnings"])}')
    else:
        print('   ✓ No data coverage warnings')

    # Training Wells Validation Summary
    print('\n2. TRAINING WELLS VALIDATION:')
    print('-' * 50)
    if training_validation:
        valid_count = len(training_validation['valid_wells'])
        invalid_count = len(training_validation['invalid_wells'])
        total_count = valid_count + invalid_count

        print(f'   Valid training wells: {valid_count}/{total_count}')
        print(f'   Invalid training wells: {invalid_count}/{total_count}')

        if invalid_count > 0:
            print(f'   ⚠ {invalid_count} wells excluded from training due to insufficient data')
            for invalid_well in training_validation['invalid_wells'][:3]:
                print(f'     • {invalid_well["well"]}: {invalid_well["reason"]}')
            if len(training_validation['invalid_wells']) > 3:
                print(f'     ... and {len(training_validation["invalid_wells"]) - 3} more')
        else:
            print('   ✓ All training wells have sufficient data')
    else:
        print('   N/A (Mixed mode - validation not applicable)')

    # Training Results Summary
    print('\n3. MODEL TRAINING RESULTS:')
    print('-' * 50)
    if selected_target_log in model_results:
        target_results = model_results[selected_target_log]
        training_errors = target_results.get('training_errors', [])
        training_warnings = target_results.get('training_warnings', [])

        if target_results.get('best_model_name'):
            print(f'   ✓ Training successful: {target_results["best_model_name"]} (MAE: {target_results["best_mae"]:.3f})')
        else:
            print('   ✗ Training failed: No successful models')

        if training_errors:
            print(f'   ✗ Training errors: {len(training_errors)}')
            for error in training_errors[:2]:
                print(f'     • {error}')
            if len(training_errors) > 2:
                print(f'     ... and {len(training_errors) - 2} more')

        if training_warnings:
            print(f'   ⚠ Training warnings: {len(training_warnings)}')
    else:
        print('   ✗ No training results available')

    # Overall Assessment
    print('\n4. OVERALL ASSESSMENT:')
    print('-' * 50)

    critical_issues = len(qc_report.get('errors', []))
    if training_validation:
        critical_issues += len(training_validation.get('invalid_wells', []))
    if selected_target_log in model_results:
        critical_issues += len(model_results[selected_target_log].get('training_errors', []))

    warnings_count = len(qc_report.get('warnings', []))
    if training_validation:
        warnings_count += len(training_validation.get('warnings', []))
    if selected_target_log in model_results:
        warnings_count += len(model_results[selected_target_log].get('training_warnings', []))

    if critical_issues == 0 and warnings_count == 0:
        print('   ✓ EXCELLENT: All QC checks passed. High confidence in results.')
        recommendation = 'Proceed with confidence. Results should be reliable.'
    elif critical_issues == 0 and warnings_count <= 3:
        print('   ✓ GOOD: Minor warnings only. Results should be reliable.')
        recommendation = 'Proceed with results. Monitor performance on new data.'
    elif critical_issues <= 2 and warnings_count <= 5:
        print('   ⚠ MODERATE: Some issues found. Results may have limitations.')
        recommendation = 'Use results with caution. Consider additional data or validation.'
    else:
        print('   ✗ POOR: Multiple critical issues. Results may be unreliable.')
        recommendation = 'Review data quality and well selection before using results.'

    print(f'   Total critical issues: {critical_issues}')
    print(f'   Total warnings: {warnings_count}')
    print(f'\n5. RECOMMENDATION:')
    print('-' * 50)
    print(f'   {recommendation}')

    print('='*100)

# Generate final comprehensive report
generate_final_qc_report(qc_report, training_validation, model_results, selected_target_log)

# Generate enhanced visualization
create_enhanced_visualization(results, model_results, selected_target_log, well_separation_config)

# Enhanced write-back function with well separation awareness
def write_back_to_petrel_enhanced(results_df, log_name_in_results, clone_from, well_config, new_log_name=None):
    """Enhanced write-back function with well separation awareness."""
    if new_log_name is None:
        new_log_name = f'{selected_target_log}_ML_enhanced'

    print(f'\nWriting {log_name_in_results} as {new_log_name}...')

    # Determine which wells to write back to based on configuration
    if well_config['mode'] == 'separated':
        # Write back only to prediction wells and mixed wells
        target_wells = well_config['prediction_wells'] + well_config['mixed_wells']
        print(f'Well separation mode: Writing to prediction wells only ({len(target_wells)} wells)')
    else:
        # Traditional mode: write to all wells
        target_wells = well_config['mixed_wells']
        print(f'Mixed mode: Writing to all wells ({len(target_wells)} wells)')

    # Helper function to find global well log by name
    def find_global_well_log_by_name(name):
        """Helper function to find global well log by name"""
        for item in petrel.global_well_logs:
            if isinstance(item, list):
                for obj in item:
                    if hasattr(obj, 'petrel_name') and obj.petrel_name == name:
                        return obj
            elif hasattr(item, 'petrel_name') and item.petrel_name == name:
                return item
        return None

    # Helper to handle Petrel collections that may contain lists
    def get_object_list(collection):
        object_list = []
        for item in collection:
            if isinstance(item, list):
                object_list.extend(item)
            else:
                object_list.append(item)
        return object_list

    # Find template log
    log_to_clone = find_global_well_log_by_name(clone_from)
    if log_to_clone is None:
        print(f"ERROR: Template log {clone_from} not found")
        return

    # Get well objects for target wells
    target_well_objects = [w for w in all_selected_wells if w.petrel_name in target_wells]

    success_count = 0
    for w in target_well_objects:
        well_df = results_df[results_df['WELL'] == w.petrel_name]
        if well_df.empty:
            continue

        # Clean data
        md_values = well_df['MD'].values
        log_values = well_df[log_name_in_results].values
        valid_mask = ~np.isnan(md_values) & ~np.isnan(log_values)

        if not valid_mask.any():
            continue

        md_clean = md_values[valid_mask].tolist()
        values_clean = log_values[valid_mask].tolist()

        try:
            # Determine collection type and get objects
            if isinstance(log_to_clone, DiscreteGlobalWellLog):
                gwl_collection = petrel.discrete_global_well_logs
            else:
                gwl_collection = petrel.global_well_logs

            # Check if global log exists using proper collection handling
            gwl = [i for i in get_object_list(gwl_collection) if hasattr(i, 'petrel_name') and i.petrel_name == new_log_name]

            # Create global log if needed
            if not gwl:
                global_log = log_to_clone.clone(name_of_clone=new_log_name)
                gwl = [global_log]

            # Check well log using proper collection handling
            well_log = [i for i in get_object_list(w.logs) if hasattr(i, 'petrel_name') and i.petrel_name == new_log_name]

            if well_log:
                # Update existing
                well_log[0].readonly = False
                well_log[0].set_values(md_clean, values_clean)
            else:
                # Create new
                new_log = gwl[0].create_well_log(w)
                new_log.readonly = False
                new_log.set_values(md_clean, values_clean)

            success_count += 1
            print(f'  ✓ {w.petrel_name}')

        except Exception as e:
            print(f'  ✗ {w.petrel_name}: {str(e)}')

    print(f'\n✓ Enhanced write-back completed: {success_count}/{len(target_well_objects)} wells updated')

# Enhanced session management
print(f"\n{'='*60}")
print("ENHANCED SESSION MANAGEMENT")
print('='*60)
print("Choose action:")
print("  1. Write results to Petrel")
print("  2. Show configuration summary")
print("  3. End session")
print("  4. Continue working")

print("\nSelect your choice:")
if not AUTO_MODE:
    print("Default: 4")
    print("Enter selection (1-4) or press Enter for default:")

    while True:
        try:
            user_input = input("Selection: ").strip()
            if not user_input:
                session_choice = 4
                break
            else:
                session_choice = int(user_input)
                if session_choice in [1, 2, 3, 4]:
                    break
                else:
                    print("Invalid selection. Please enter 1, 2, 3, or 4.")
        except (ValueError, EOFError, KeyboardInterrupt):
            print("Invalid input. Using default selection...")
            session_choice = 4
            break
else:
    session_choice = 4
    print("Auto-selected: 4")

# Display selected choice
choice_names = {1: "Write results to Petrel", 2: "Show configuration summary", 3: "End session", 4: "Continue working"}
print(f"Selected: {session_choice}. {choice_names[session_choice]}")

if session_choice == 1:
    print('\n' + '='*60)
    print('WRITING ENHANCED RESULTS TO PETREL')
    print('='*60)

    log_to_write = f'{selected_target_log}_imputed'
    new_log_name = f'{selected_target_log}_ML_enhanced'

    try:
        write_back_to_petrel_enhanced(results, log_to_write, selected_target_log,
                                    well_separation_config, new_log_name)
        print('✓ Enhanced write-back completed successfully!')
    except Exception as e:
        print(f'✗ Error during write-back: {str(e)}')

elif session_choice == 2:
    print('\n' + '='*60)
    print('ENHANCED CONFIGURATION SUMMARY')
    print('='*60)
    print(f'Project: {petrel.get_current_project_name()}')
    print(f'Target Log: {selected_target_log}')
    print(f'Input Logs: {selected_input_logs}')
    print(f'Prediction Mode: {prediction_mode_name}')
    print(f'Well Separation Mode: {well_separation_config["mode"].upper()}')

    if well_separation_config['mode'] == 'separated':
        print(f'Training Wells ({len(well_separation_config["training_wells"])}): {well_separation_config["training_wells"]}')
        print(f'Prediction Wells ({len(well_separation_config["prediction_wells"])}): {well_separation_config["prediction_wells"]}')
        if well_separation_config['mixed_wells']:
            print(f'Mixed Wells ({len(well_separation_config["mixed_wells"])}): {well_separation_config["mixed_wells"]}')
    else:
        print(f'All Wells ({len(well_separation_config["mixed_wells"])}): {well_separation_config["mixed_wells"]}')

    print(f'\nHyperparameters:')
    for model_name, params in hyperparameter_config.items():
        print(f'  {model_name.upper()}: {params}')

    if selected_target_log in model_results:
        best_model = model_results[selected_target_log]['best_model_name']
        best_mae = model_results[selected_target_log]['best_mae']
        print(f'\nBest Model: {best_model} (MAE: {best_mae:.3f})')

    print('='*60)

elif session_choice == 3:
    print('\n✓ Session ending...')
    print("Connection management:")
    print("  1. Close Petrel connection")
    print("  2. Keep connection open")

    print("\nSelect your choice:")
    if not AUTO_MODE:
        print("Default: 2")
        print("Enter selection (1-2) or press Enter for default:")

        while True:
            try:
                user_input = input("Selection: ").strip()
                if not user_input:
                    connection_choice = 2
                    break
                else:
                    connection_choice = int(user_input)
                    if connection_choice in [1, 2]:
                        break
                    else:
                        print("Invalid selection. Please enter 1 or 2.")
            except (ValueError, EOFError, KeyboardInterrupt):
                print("Invalid input. Using default selection...")
                connection_choice = 2
                break
    else:
        connection_choice = 2
        print("Auto-selected: 2")

    # Display selected choice
    conn_choice_names = {1: "Close Petrel connection", 2: "Keep connection open"}
    print(f"Selected: {connection_choice}. {conn_choice_names[connection_choice]}")

    if connection_choice == 1:
        try:
            petrel.close()
            print('✓ Petrel connection closed.')
        except Exception as e:
            print(f'⚠ Error closing connection: {str(e)}')
else:
    print('\n✓ Continuing session. Enhanced results available in "results" variable.')
    print('Enhanced configuration available in "hyperparameter_config" and "well_separation_config" variables.')
    print('Call write_back_to_petrel_enhanced() manually if needed later.')

# Enhanced final summary
print(f"\n{'='*80}")
print("ENHANCED WORKFLOW COMPLETED")
print('='*80)
print(f"✓ Project: {petrel.get_current_project_name()}")
print(f"✓ Target log: {selected_target_log}")
print(f"✓ Prediction mode: {prediction_mode_name}")
print(f"✓ Well separation mode: {well_separation_config['mode'].upper()}")

if well_separation_config['mode'] == 'separated':
    print(f"✓ Training wells: {len(well_separation_config['training_wells'])}")
    print(f"✓ Prediction wells: {len(well_separation_config['prediction_wells'])}")
    if well_separation_config['mixed_wells']:
        print(f"✓ Mixed wells: {len(well_separation_config['mixed_wells'])}")
else:
    print(f"✓ Wells processed: {len(well_separation_config['mixed_wells'])}")

print(f"✓ Results shape: {results.shape}")

if selected_target_log in model_results:
    best_model = model_results[selected_target_log]['best_model_name']
    best_mae = model_results[selected_target_log]['best_mae']
    print(f"✓ Best model: {best_model} (MAE: {best_mae:.3f})")

print(f"\nENHANCED RESULTS AVAILABLE:")
print("- results: Main DataFrame with predictions")
print("- model_results: Model performance data with hyperparameters")
print("- hyperparameter_config: User-configured hyperparameters")
print("- well_separation_config: Well separation configuration")
print('='*80)
