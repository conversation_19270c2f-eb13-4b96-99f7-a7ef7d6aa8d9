# Enhanced Log Imputation Script - Version 2

## Overview
The `Logs_imputation_petrel_enhanced_v2.py` file is an enhanced version of the original optimized log imputation script with two major new features:

1. **Interactive Hyperparameter Configuration System**
2. **Well Separation Feature for Training vs Prediction**

## New Features

### 1. Hyperparameter Input System

The enhanced version includes an interactive hyperparameter configuration system that allows users to customize machine learning model parameters:

#### Supported Models and Parameters:

**XGBoost** (Gradient Boosting with GPU acceleration):
- **Core Parameters**: n_estimators, learning_rate
- **Advanced Parameters**: max_depth, subsample, colsample_bytree, reg_alpha, reg_lambda, gamma
- **Fixed Parameters**: tree_method='gpu_hist', early_stopping_rounds=100, random_state=42

**LightGBM** (Fast gradient boosting with excellent defaults):
- **Core Parameters**: n_estimators
- **Advanced Parameters**: learning_rate, max_depth, subsample, colsample_bytree, reg_alpha, reg_lambda, min_child_samples
- **Fixed Parameters**: device='gpu', random_state=42

**CatBoost** (Handles categorical features automatically):
- **Core Parameters**: iterations, learning_rate (auto-determined if not specified)
- **Advanced Parameters**: depth, l2_leaf_reg, subsample, colsample_bylevel, random_strength
- **Fixed Parameters**: task_type='GPU', early_stopping_rounds=100, verbose=0, random_state=42

#### How it Works:
- Interactive prompts guide users through parameter configuration
- Default values are provided for all parameters
- Input validation ensures parameters are within acceptable ranges
- Auto-mode option uses default parameters without user interaction

#### Example Usage:
```
--- XGBoost Configuration ---
XGBoost is a powerful gradient boosting framework optimized for speed and performance.
Number of estimators (trees) - More trees = better fit but slower training (default: 300): 500
Learning rate - Lower = more conservative, higher = faster convergence (default: 0.05): 0.1

--- XGBoost Advanced Parameters (Optional) ---
Press Enter to use recommended defaults, or specify custom values:
Max tree depth - Deeper trees can capture more patterns but may overfit (recommended: 6): 8
Subsample ratio - Fraction of samples used per tree (recommended: 0.8-1.0): 0.9
Column sampling - Fraction of features used per tree (recommended: 0.8-1.0):
L1 regularization - Helps with feature selection (recommended: 0-1):
L2 regularization - Helps prevent overfitting (recommended: 1):
Gamma (min split loss) - Higher values make model more conservative (recommended: 0):

--- LightGBM Configuration ---
LightGBM is a fast gradient boosting framework with excellent default parameters.
Number of estimators (trees) - More trees = better fit but slower training (default: 300): 400

--- LightGBM Advanced Parameters (Optional) ---
Learning rate - Lower = more conservative, higher = faster convergence (recommended: 0.1):
Max tree depth - -1 for no limit, 6-10 for controlled depth (recommended: -1):
[Additional parameters with recommendations...]

--- CatBoost Configuration ---
CatBoost has excellent default parameters and handles categorical features automatically.

--- CatBoost Advanced Parameters (Optional) ---
CatBoost works very well with defaults. Press Enter to use recommended values:
Number of iterations (trees) - More iterations = better fit (recommended: 1000):
Learning rate - Leave empty for auto-determination (recommended):
Tree depth - Deeper trees capture more patterns but may overfit (recommended: 6):
[Additional parameters with recommendations...]
```

### 2. Well Separation Feature

The well separation system allows users to designate specific wells for different purposes:

#### Well Types:
- **Training Wells**: Wells with complete log data used solely for model training
- **Prediction Wells**: Wells with missing log curves where predictions will be applied
- **Mixed Wells**: Wells used for both training (complete data) and prediction (missing data)

#### Separation Modes:
1. **Separated Mode**: Distinct training and prediction wells
2. **Mixed Mode**: Traditional approach using all wells for both training and prediction

#### Benefits:
- Improved model training by using only high-quality complete data
- Targeted prediction application to wells that actually need imputation
- Better control over data quality and model performance
- Realistic evaluation of model performance on unseen wells

### 3. Enhanced Visualization

The visualization system has been upgraded to show:
- Well type indicators (Training/Prediction/Mixed) with color coding
- Performance metrics per well type
- Hyperparameter information in summaries
- Well separation mode indicators

### 4. Enhanced Hyperparameter System

The hyperparameter configuration system now includes:
- **Educational Prompts**: Each parameter includes explanations and recommendations
- **Best Practice Defaults**: All defaults are based on official documentation research
- **Advanced Options**: Optional parameters for fine-tuning with clear guidance
- **Parameter Explanations**: Real-time explanations of what each parameter does
- **Fixed Essential Parameters**: Critical parameters (GPU, early stopping, reproducibility) are automatically set

#### Parameter Categories:
- **Core Parameters**: Essential tuning parameters (n_estimators, learning_rate)
- **Advanced Parameters**: Optional fine-tuning parameters with recommended defaults
- **Fixed Parameters**: Essential parameters automatically set for optimal performance

#### Hyperparameter Recommendations:

**For Beginners**: Use default values for all parameters - they're optimized for most use cases.

**For Intermediate Users**: Adjust core parameters:
- Increase `n_estimators` for better performance (if you have time)
- Decrease `learning_rate` for more stable training
- Adjust `max_depth` to control overfitting

**For Advanced Users**: Fine-tune advanced parameters:
- Use regularization (`reg_alpha`, `reg_lambda`) for overfitting control
- Adjust sampling ratios (`subsample`, `colsample_bytree`) for robustness
- Tune model-specific parameters based on data characteristics

### 5. Enhanced Write-back System

The write-back functionality now:
- Respects well separation configuration
- Only writes predictions to appropriate wells (prediction + mixed wells in separated mode)
- Provides detailed feedback on which wells were updated
- Maintains compatibility with existing Petrel write-back patterns

## Usage Instructions

### Basic Workflow:
1. **Run the script**: `python Logs_imputation_petrel_enhanced_v2.py`
2. **Configure hyperparameters**: Set custom parameters or use defaults
3. **Configure well separation**: Choose separated or mixed mode
4. **Select wells and logs**: Choose training/prediction wells and input/target logs
5. **Select prediction mode**: Choose from the three available modes
6. **Review results**: Examine enhanced visualizations and performance metrics
7. **Write back to Petrel**: Optionally save results to Petrel project

### Configuration Options:

#### Auto Mode:
Set `AUTO_MODE = True` at the top of the script to use default configurations without user interaction.

#### Well Separation Modes:
- **Mode 1 (Separated)**: Designate specific wells for training vs prediction
- **Mode 2 (Mixed)**: Use all wells for both training and prediction (traditional)

#### Prediction Modes:
- **Mode 1**: Fill Missing Values Only
- **Mode 2**: Cross-Validation Test
- **Mode 3**: Re-predict Entire Log

## Key Improvements Over Original

### Enhanced Data Management:
- Separate tracking of training and prediction data
- Well type labeling in datasets
- Improved data loading with well separation support

### Advanced Model Configuration:
- User-customizable hyperparameters
- Model performance tracking with hyperparameter information
- Enhanced model comparison capabilities

### Improved User Experience:
- Clear step-by-step configuration process
- Detailed feedback and progress indicators
- Enhanced session management with configuration summaries

### Better Results Tracking:
- Hyperparameter information stored in model results
- Well separation configuration preserved
- Enhanced summary statistics and reporting

## Compatibility

The enhanced version maintains full compatibility with:
- All existing Petrel connection patterns
- Original visualization preferences (matplotlib over plotly)
- Existing write-back functionality
- All three prediction modes from the original script

## Variables Available After Execution

After running the script, the following variables are available:
- `results`: Main DataFrame with predictions and well type information
- `model_results`: Enhanced model performance data with hyperparameters
- `hyperparameter_config`: User-configured hyperparameters for all models
- `well_separation_config`: Well separation configuration and mode
- `training_wells`: List of wells designated for training
- `prediction_wells`: List of wells designated for prediction
- `all_selected_wells`: Combined list of all selected wells

## Example Configuration Summary

```
ENHANCED CONFIGURATION SUMMARY
============================================================
Project: MyPetrelProject
Target Log: Vs
Input Logs: ['GR', 'RHOB', 'NPHI', 'Vp']
Prediction Mode: Fill Missing Values Only
Well Separation Mode: SEPARATED
Training Wells (3): ['Well_A', 'Well_B', 'Well_C']
Prediction Wells (2): ['Well_D', 'Well_E']

Hyperparameters:
  XGBOOST: {'n_estimators': 500, 'learning_rate': 0.1, 'max_depth': 8, ...}
  LIGHTGBM: {'n_estimators': 400, 'learning_rate': 0.05, 'max_depth': 10, ...}
  CATBOOST: {'iterations': 300, 'learning_rate': 0.1, 'depth': 6, ...}

Best Model: XGBoost (MAE: 0.045)
============================================================
```

This enhanced version provides significantly more control and flexibility while maintaining the simplicity and effectiveness of the original optimized script.
