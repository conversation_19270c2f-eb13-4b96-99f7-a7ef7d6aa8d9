"""
Quick Fix for write_back_to_petrel Function
==========================================

This script provides an immediate fix for the NameError by defining the 
write_back_to_petrel function in your current session.

Run this script in your Python Tool Pro environment to immediately 
resolve the NameError and enable write-back functionality.

Usage:
    exec(open('JPY_PRJ/quick_fix_writeback.py').read())
    
Or simply run:
    python JPY_PRJ/quick_fix_writeback.py
"""

import pandas as pd
import numpy as np

def write_back_log_robust(petrel, log_name: str, log_to_clone, well, md: list, values: list, template=None):
    """
    Robust function to write log data back to a well in Petrel.
    """
    print(f"DEBUG: Starting write_back_log_robust for {log_name} in well {well.petrel_name}")
    print(f"DEBUG: MD values count: {len(md)}, Log values count: {len(values)}")
    print(f"DEBUG: Log to clone: {log_to_clone.petrel_name}")

    try:
        from cegalprizm.pythontool import DiscreteGlobalWellLog
        
        # Helper to handle Petrel collections that may contain lists
        def get_object_list(collection):
            object_list = []
            for item in collection:
                if isinstance(item, list):
                    object_list.extend(item)
                else:
                    object_list.append(item)
            return object_list

        print("DEBUG: Determining global log collection type")
        if isinstance(log_to_clone, DiscreteGlobalWellLog):
            gwl_collection = petrel.discrete_global_well_logs
            print("DEBUG: Using discrete global well logs collection")
        else:
            gwl_collection = petrel.global_well_logs
            print("DEBUG: Using continuous global well logs collection")

        print(f"DEBUG: Searching for existing global log: {log_name}")
        gwl = [i for i in get_object_list(gwl_collection) if i.petrel_name == log_name]

        # If global well log doesn't exist, create a new one.
        if not gwl:
            print(f'Creating new global well log: {log_name}')
            print("DEBUG: Cloning global log from template")
            global_log = log_to_clone.clone(name_of_clone=log_name, template=template)
            gwl = [global_log]
            print("DEBUG: New global log created successfully")
        else:
            print(f"DEBUG: Found existing global log: {log_name}")

        print("DEBUG: Checking for existing well log in target well")
        well_log = [i for i in get_object_list(well.logs) if i.petrel_name == log_name]

        # If well has the log, overwrite its values.
        if len(well_log) == 1:
            print("DEBUG: Well log exists, overwriting values")
            well_log[0].readonly = False
            well_log[0].set_values(md, values)
            print(f"Values for {log_name} overwritten for {well.petrel_name}")
            print("DEBUG: Well log values overwritten successfully")
        # If well does not have this log, create it and set its values.
        elif not well_log:
            print(f'Creating new well log: {log_name} for well {well.petrel_name}')
            print("DEBUG: Creating new well log from global log")
            new_log = gwl[0].create_well_log(well)
            new_log.readonly = False
            new_log.set_values(md, values)
            print(f"New well log {log_name} created for well {well.petrel_name}")
            print("DEBUG: New well log created and values set successfully")
        else:
            print(f"WARNING: Multiple well logs found with name {log_name} in well {well.petrel_name}")

        print(f"DEBUG: write_back_log_robust completed successfully for {log_name}")
        return True

    except Exception as e:
        print(f"ERROR: Failed to write back log {log_name} for well {well.petrel_name}: {str(e)}")
        print(f"DEBUG: Exception type: {type(e)}")
        print(f"DEBUG: MD values sample: {md[:5] if len(md) > 5 else md}")
        print(f"DEBUG: Log values sample: {values[:5] if len(values) > 5 else values}")
        import traceback
        print(f"DEBUG: Traceback: {traceback.format_exc()}")
        return False


def write_back_to_petrel(results_df, log_name_in_results, clone_from=None, new_log_name=None):
    """
    Enhanced write-back function using robust implementation pattern.
    """
    # Check for required variables in global scope
    if 'selected_wells' not in globals():
        print('ERROR: selected_wells not found in global scope.')
        print('Please ensure you have run the well selection steps first.')
        return
    
    if 'petrel' not in globals():
        print('ERROR: petrel connection not found in global scope.')
        print('Please ensure you have established a Petrel connection.')
        return

    if clone_from is None:
        if 'selected_target_log' in globals():
            clone_from = selected_target_log
        else:
            clone_from = 'VP'  # Default fallback
            print(f"WARNING: Using default template log: {clone_from}")

    # Auto-generate new log name if not provided
    if new_log_name is None:
        if '_imputed' in log_name_in_results:
            new_log_name = log_name_in_results.replace('_imputed', '_ML_imputed')
        elif '_pred' in log_name_in_results:
            new_log_name = log_name_in_results.replace('_pred', '_ML_predicted')
        elif '_repredicted' in log_name_in_results:
            new_log_name = log_name_in_results  # Keep the same name
        else:
            new_log_name = f'{log_name_in_results}_ML'

    print(f'Writing back {log_name_in_results} as {new_log_name} to {len(selected_wells)} wells...')
    print(f'Using robust write-back implementation pattern')
    
    # Find the global log to use as template
    print(f"DEBUG: Searching for global log template: {clone_from}")
    
    def find_global_well_log_by_name(name):
        """Helper function to find global well log by name"""
        for item in petrel.global_well_logs:
            if isinstance(item, list):
                for obj in item:
                    if hasattr(obj, 'petrel_name') and obj.petrel_name == name:
                        return obj
            elif hasattr(item, 'petrel_name') and item.petrel_name == name:
                return item
        return None
    
    log_to_clone = find_global_well_log_by_name(clone_from)
    if log_to_clone is None:
        print(f"ERROR: Could not find global well log with name: {clone_from}")
        print("Available global logs:")
        try:
            available_logs = [log.petrel_name for log in petrel.global_well_logs if hasattr(log, 'petrel_name')]
            for log_name in available_logs[:10]:  # Show first 10
                print(f"  - {log_name}")
            if len(available_logs) > 10:
                print(f"  ... and {len(available_logs) - 10} more")
        except:
            print("  Could not retrieve available logs")
        return
    
    print(f"DEBUG: Found global log template: {log_to_clone.petrel_name}")
    
    success_count = 0
    total_wells = len(selected_wells)
    
    for w in selected_wells:
        print(f'\nProcessing well: {w.petrel_name} ({success_count + 1}/{total_wells})')
        
        # Filter data for this well
        well_df = results_df[results_df['WELL'] == w.petrel_name]
        if well_df.empty:
            print(f'  ⚠ No data for well {w.petrel_name}, skipping')
            continue
        
        # Extract MD and values, ensuring no NaN values
        md_values = well_df['MD'].values
        log_values = well_df[log_name_in_results].values
        
        # Clean NaN values
        valid_mask = ~np.isnan(md_values) & ~np.isnan(log_values)
        md_clean = md_values[valid_mask].tolist()
        values_clean = log_values[valid_mask].tolist()
        
        if not md_clean:
            print(f'  ⚠ No valid data points for well {w.petrel_name}, skipping')
            continue
            
        print(f'  Valid data points: {len(md_clean)}')
        
        # Use robust write-back function
        try:
            success = write_back_log_robust(
                petrel=petrel,
                log_name=new_log_name,
                log_to_clone=log_to_clone,
                well=w,
                md=md_clean,
                values=values_clean,
                template=None
            )
            
            if success:
                print(f'  ✓ Successfully updated {new_log_name} for {w.petrel_name}')
                success_count += 1
            else:
                print(f'  ✗ Failed to update {new_log_name} for {w.petrel_name}')
                
        except Exception as e:
            print(f'  ✗ Error updating {new_log_name} for {w.petrel_name}: {str(e)}')
    
    print(f'\n{"="*60}')
    print(f'WRITE-BACK COMPLETED: {success_count}/{total_wells} wells updated successfully')
    print(f'New log name: {new_log_name}')
    print(f'{"="*60}')
    
    return success_count > 0


# Make functions available in global scope
globals()['write_back_to_petrel'] = write_back_to_petrel
globals()['write_back_log_robust'] = write_back_log_robust

print("✓ write_back_to_petrel function has been defined and is now available!")
print("✓ write_back_log_robust function has been defined and is now available!")
print("\nYou can now use:")
print("write_back_to_petrel(results, 'VP_COREL_ML_repredicted', clone_from='VP')")
print("\nOr retry your original write-back operation.")
