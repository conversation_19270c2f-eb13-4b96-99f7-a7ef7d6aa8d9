"""
Test version of the enhanced script with AUTO_MODE enabled
This tests the complete workflow without requiring user input.
"""

# Core libraries
import numpy as np
import pandas as pd
import warnings
warnings.filterwarnings('ignore')

# Petrel connection
from cegalprizm.pythontool import PetrelConnection

def console_select_multiple(options, prompt, default_selections=None, max_selections=None, auto_mode=True):
    """Auto-select multiple options"""
    if not options:
        print("No options available for selection.")
        return []
    
    print(f"\n{prompt}")
    print(f"Available options: {len(options)} items")
    print(f"First few: {options[:5]}")
    
    # Auto mode: use defaults without user input
    if auto_mode:
        if default_selections:
            selected = [opt for opt in default_selections if opt in options]
            if selected:
                print(f"Auto-selected: {selected}")
                return selected
        
        # Fallback to first few options
        max_select = min(max_selections or 5, len(options))
        selected = options[:max_select]
        print(f"Auto-selected (fallback): {selected}")
        return selected

def console_select_single(options, prompt, default_selection=None, auto_mode=True):
    """Auto-select single option"""
    if not options:
        print("No options available for selection.")
        return None
    
    print(f"\n{prompt}")
    print(f"Available options: {len(options)} items")
    print(f"First few: {options[:5]}")
    
    # Auto mode: use default without user input
    if auto_mode:
        if default_selection and default_selection in options:
            print(f"Auto-selected: {default_selection}")
            return default_selection
        else:
            selected = options[0]
            print(f"Auto-selected (fallback): {selected}")
            return selected

def main():
    """Main test function"""
    print("="*60)
    print("TESTING ENHANCED SCRIPT WITH AUTO-MODE")
    print("="*60)
    
    try:
        # Connect to Petrel
        petrel = PetrelConnection()
        print(f'✓ Connected to Petrel project: {petrel.get_current_project_name()}')
        
        # Get all available global well logs
        print('\nScanning global well logs...')
        available_logs = {}
        try:
            for log in petrel.global_well_logs:
                if hasattr(log, 'petrel_name'):
                    available_logs[log.petrel_name] = log
            
            log_names = sorted(available_logs.keys())
            print(f'✓ Found {len(log_names)} global well logs')
            
            if len(log_names) > 0:
                print(f'First few logs: {log_names[:5]}')
                
        except Exception as e:
            print(f'✗ Error accessing global well logs: {str(e)}')
            return False
        
        # Get all wells
        print('\nScanning wells...')
        try:
            wells = list(petrel.wells)
            well_names = [w.petrel_name for w in wells]
            print(f'✓ Found {len(wells)} wells')
            
            if len(wells) > 0:
                print(f'First few wells: {well_names[:3]}')
            
        except Exception as e:
            print(f'✗ Error accessing wells: {str(e)}')
            return False
        
        # Check if we have data
        if not wells or not well_names:
            print('✗ No wells found!')
            return False
        
        if not log_names:
            print('✗ No logs found!')
            return False
        
        # Auto-select wells
        default_wells = well_names[:min(3, len(well_names))]  # First 3 wells
        selected_well_names = console_select_multiple(
            options=well_names,
            prompt="Select Wells for Analysis:",
            default_selections=default_wells,
            max_selections=3,
            auto_mode=True
        )
        
        # Auto-select input logs
        default_input_logs = ['GR', 'RHOB', 'NPHI', 'Vp']
        selected_input_logs = console_select_multiple(
            options=log_names,
            prompt="Select Input Logs for ML Training:",
            default_selections=default_input_logs,
            max_selections=None,
            auto_mode=True
        )
        
        # Auto-select target log
        default_target_log = 'Vs'
        selected_target_log = console_select_single(
            options=log_names,
            prompt="Select Target Log for Imputation:",
            default_selection=default_target_log,
            auto_mode=True
        )
        
        # Get selected wells objects
        selected_wells = [w for w in wells if w.petrel_name in selected_well_names]
        
        # Combine all log names
        LOG_NAMES = selected_input_logs + [selected_target_log]
        print(f'\n✓ All logs to process: {LOG_NAMES}')
        
        # Find global well logs by name
        def find_global_well_logs_by_names(names):
            found_logs = []
            for name in names:
                if name in available_logs:
                    found_logs.append(available_logs[name])
                else:
                    print(f'Warning: Log {name} not found in available logs')
            return found_logs
        
        logs = find_global_well_logs_by_names(LOG_NAMES)
        print(f'✓ Using {len(logs)} global logs: {[g.petrel_name for g in logs]}')
        
        # Test data loading from first well
        if selected_wells and logs:
            print(f'\n--- Testing data loading from: {selected_wells[0].petrel_name} ---')
            try:
                df = selected_wells[0].logs_dataframe(logs)
                if not df.empty:
                    print(f'✓ Successfully loaded data: {df.shape}')
                    print(f'Columns: {list(df.columns)}')
                    print(f'Index (MD) range: {df.index.min():.1f} - {df.index.max():.1f}')
                    
                    # Check data coverage
                    coverage = 1.0 - df.isna().mean()
                    print('\nData coverage per log:')
                    for log_name, cov in coverage.items():
                        print(f'  {log_name}: {cov:.2%}')
                        
                    print('\n✓ Data loading test successful!')
                else:
                    print('⚠ No data found for this well')
            except Exception as e:
                print(f'✗ Error loading data: {str(e)}')
                return False
        
        print(f'\n✓ All tests passed!')
        print(f'✓ Wells: {len(selected_wells)} selected from {len(wells)} available')
        print(f'✓ Logs: {len(logs)} selected from {len(log_names)} available')
        print(f'✓ The enhanced script should work correctly!')
        
        return True
        
    except Exception as e:
        print(f'✗ Error during testing: {str(e)}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n" + "="*60)
        print("SUCCESS: Enhanced script ready for use!")
        print("Set AUTO_MODE = False for interactive selection")
        print("Set AUTO_MODE = True for automatic selection")
        print("="*60)
    else:
        print("\n" + "="*60)
        print("FAILURE: Issues need to be resolved")
        print("="*60)
