# Test script for enhanced visualization improvements
import ast
import sys

def test_syntax():
    """Test syntax of enhanced logs imputation file."""
    try:
        with open('JPY_PRJ/Logs_imputation_petrel_enhanced_v2.py', 'r') as f:
            content = f.read()
        
        # Parse the file to check syntax
        ast.parse(content)
        print("✅ SYNTAX VALIDATION PASSED")
        return True
        
    except SyntaxError as e:
        print(f"❌ SYNTAX ERROR: {e}")
        print(f"   Line {e.lineno}: {e.text}")
        return False
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def test_function_definitions():
    """Test that key functions are properly defined."""
    try:
        with open('JPY_PRJ/Logs_imputation_petrel_enhanced_v2.py', 'r') as f:
            content = f.read()
        
        # Check for key function definitions
        required_functions = [
            'evaluate_model_comprehensive',
            'create_enhanced_visualization',
            'impute_logs_enhanced'
        ]
        
        missing_functions = []
        for func in required_functions:
            if f'def {func}(' not in content:
                missing_functions.append(func)
        
        if missing_functions:
            print(f"❌ MISSING FUNCTIONS: {missing_functions}")
            return False
        else:
            print("✅ ALL KEY FUNCTIONS FOUND")
            return True
            
    except Exception as e:
        print(f"❌ ERROR CHECKING FUNCTIONS: {e}")
        return False

def test_import_statements():
    """Test that required imports are present."""
    try:
        with open('JPY_PRJ/Logs_imputation_petrel_enhanced_v2.py', 'r') as f:
            content = f.read()
        
        # Check for required imports
        required_imports = [
            'mean_squared_error',
            'mean_absolute_percentage_error',
            'cross_val_score'
        ]
        
        missing_imports = []
        for imp in required_imports:
            if imp not in content:
                missing_imports.append(imp)
        
        if missing_imports:
            print(f"❌ MISSING IMPORTS: {missing_imports}")
            return False
        else:
            print("✅ ALL REQUIRED IMPORTS FOUND")
            return True
            
    except Exception as e:
        print(f"❌ ERROR CHECKING IMPORTS: {e}")
        return False

def test_improvements_implementation():
    """Test that specific improvements are implemented."""
    try:
        with open('JPY_PRJ/Logs_imputation_petrel_enhanced_v2.py', 'r') as f:
            content = f.read()
        
        improvements_found = []
        
        # Test 1: Enhanced model selection criteria
        if 'composite_score' in content and 'cv_mae' in content:
            improvements_found.append("✅ Enhanced Model Selection Criteria")
        else:
            improvements_found.append("❌ Enhanced Model Selection Criteria")
        
        # Test 2: Well separation visualization
        if 'training_wells_to_plot' in content and 'prediction_wells_to_plot' in content:
            improvements_found.append("✅ Separate Training/Prediction Well Visualizations")
        else:
            improvements_found.append("❌ Separate Training/Prediction Well Visualizations")
        
        # Test 3: Multi-model comparison
        if 'top_models' in content and 'models_to_visualize' in content:
            improvements_found.append("✅ Multi-Model Comparison Visualizations")
        else:
            improvements_found.append("❌ Multi-Model Comparison Visualizations")
        
        print("\n📊 IMPROVEMENTS IMPLEMENTATION STATUS:")
        for improvement in improvements_found:
            print(f"   {improvement}")
        
        return all("✅" in imp for imp in improvements_found)
        
    except Exception as e:
        print(f"❌ ERROR CHECKING IMPROVEMENTS: {e}")
        return False

def main():
    """Run all tests."""
    print("🔍 TESTING ENHANCED LOGS IMPUTATION IMPROVEMENTS")
    print("=" * 60)
    
    tests = [
        ("Syntax Validation", test_syntax),
        ("Function Definitions", test_function_definitions),
        ("Import Statements", test_import_statements),
        ("Improvements Implementation", test_improvements_implementation)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 Testing {test_name}...")
        result = test_func()
        results.append(result)
    
    print("\n" + "=" * 60)
    print("📋 FINAL TEST RESULTS:")
    print("=" * 60)
    
    for i, (test_name, _) in enumerate(tests):
        status = "✅ PASSED" if results[i] else "❌ FAILED"
        print(f"   {test_name}: {status}")
    
    overall_success = all(results)
    print(f"\n🎯 OVERALL STATUS: {'✅ ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
    
    if overall_success:
        print("\n🎉 All three improvements have been successfully implemented:")
        print("   1. ✅ Research-based Enhanced Model Selection Criteria")
        print("   2. ✅ Separate Training/Prediction Well Visualizations")
        print("   3. ✅ Multi-Model Comparison Visualizations")
        print("\n🚀 The enhanced logs imputation system is ready for use!")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
