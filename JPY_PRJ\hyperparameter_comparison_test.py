#!/usr/bin/env python3
"""
Hyperparameter Comparison Test
Verifies that the enhanced version's default hyperparameters match exactly 
with the reference optimized version.
"""

# Reference file hyperparameters (from Logs_imputation_petrel_optimized.py)
reference_hyperparams = {
    'xgboost': {
        'n_estimators': 300,
        'tree_method': 'gpu_hist',
        'learning_rate': 0.05,
        'early_stopping_rounds': 100,
        'random_state': 42
    },
    'lightgbm': {
        'device': 'gpu',
        'n_estimators': 300,
        'random_state': 42
    },
    'catboost': {
        'task_type': 'GPU',
        'early_stopping_rounds': 100,
        'verbose': 0,
        'random_state': 42
    }
}

# Enhanced version default hyperparameters (from get_hyperparameter_config function)
enhanced_default_hyperparams = {
    'xgboost': {
        'n_estimators': 300,
        'tree_method': 'gpu_hist',
        'learning_rate': 0.05,
        'early_stopping_rounds': 100,
        'random_state': 42
    },
    'lightgbm': {
        'device': 'gpu',
        'n_estimators': 300,
        'random_state': 42
    },
    'catboost': {
        'task_type': 'GPU',
        'early_stopping_rounds': 100,
        'verbose': 0,
        'random_state': 42
    }
}

def compare_hyperparameters():
    """Compare hyperparameters between reference and enhanced versions."""
    print("="*60)
    print("HYPERPARAMETER COMPARISON TEST")
    print("="*60)
    
    all_match = True
    
    for model_name in reference_hyperparams.keys():
        print(f"\n--- {model_name.upper()} ---")
        
        ref_params = reference_hyperparams[model_name]
        enh_params = enhanced_default_hyperparams[model_name]
        
        # Check if all reference parameters exist in enhanced version
        for param, value in ref_params.items():
            if param not in enh_params:
                print(f"❌ MISSING: {param} not found in enhanced version")
                all_match = False
            elif enh_params[param] != value:
                print(f"❌ MISMATCH: {param}")
                print(f"   Reference: {value}")
                print(f"   Enhanced:  {enh_params[param]}")
                all_match = False
            else:
                print(f"✅ MATCH: {param} = {value}")
        
        # Check for extra parameters in enhanced version
        for param in enh_params.keys():
            if param not in ref_params:
                print(f"⚠️  EXTRA: {param} = {enh_params[param]} (not in reference)")
                # Note: Extra parameters are not necessarily bad, but should be noted
    
    print("\n" + "="*60)
    if all_match:
        print("✅ SUCCESS: All reference hyperparameters match exactly!")
        print("The enhanced version will produce identical results when using defaults.")
    else:
        print("❌ FAILURE: Hyperparameters do not match!")
        print("The enhanced version may produce different results than the reference.")
    print("="*60)
    
    return all_match

def test_model_creation():
    """Test that models can be created with the hyperparameters."""
    print("\n" + "="*60)
    print("MODEL CREATION TEST")
    print("="*60)
    
    try:
        from xgboost import XGBRegressor
        from lightgbm import LGBMRegressor
        from catboost import CatBoostRegressor
        
        # Test XGBoost
        print("Testing XGBoost creation...")
        xgb_model = XGBRegressor(**enhanced_default_hyperparams['xgboost'])
        print("✅ XGBoost model created successfully")
        
        # Test LightGBM
        print("Testing LightGBM creation...")
        lgb_model = LGBMRegressor(**enhanced_default_hyperparams['lightgbm'])
        print("✅ LightGBM model created successfully")
        
        # Test CatBoost
        print("Testing CatBoost creation...")
        cat_model = CatBoostRegressor(**enhanced_default_hyperparams['catboost'])
        print("✅ CatBoost model created successfully")
        
        print("\n✅ All models created successfully with default hyperparameters!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Some ML libraries may not be available")
        return False
    except Exception as e:
        print(f"❌ Model creation error: {e}")
        return False

if __name__ == "__main__":
    # Run comparison test
    params_match = compare_hyperparameters()
    
    # Run model creation test
    models_work = test_model_creation()
    
    # Final summary
    print("\n" + "="*60)
    print("FINAL SUMMARY")
    print("="*60)
    print(f"Hyperparameters match: {'✅ YES' if params_match else '❌ NO'}")
    print(f"Models can be created: {'✅ YES' if models_work else '❌ NO'}")
    
    if params_match and models_work:
        print("\n🎉 All tests passed! The enhanced version is ready to use.")
    else:
        print("\n⚠️  Some tests failed. Please review the issues above.")
