"""
Working Write-Back Solution
===========================

This script provides a complete, working solution for writing VP_COREL_ML_repredicted
data back to Petrel, handling all common issues.

Usage:
1. Run this script: exec(open('JPY_PRJ/working_writeback_solution.py').read())
2. Call: execute_writeback()
"""

import pandas as pd
import numpy as np

def execute_writeback():
    """
    Complete write-back execution with comprehensive error handling
    """
    print("="*60)
    print("EXECUTING WRITE-BACK TO PETREL")
    print("="*60)
    
    # Step 1: Validate environment
    print("\nStep 1: Validating environment...")
    
    # Check Petrel connection
    if 'petrel' not in globals():
        print("✗ ERROR: No Petrel connection found")
        print("Please establish connection: petrel = PetrelConnection()")
        return False
    
    petrel = globals()['petrel']
    try:
        project_name = petrel.get_current_project_name()
        print(f"✓ Petrel connected to: {project_name}")
    except Exception as e:
        print(f"✗ ERROR: Petrel connection issue: {e}")
        return False
    
    # Check wells
    if 'selected_wells' not in globals():
        print("✗ ERROR: No selected_wells found")
        print("Attempting to get wells from Petrel...")
        try:
            wells = list(petrel.wells)
            selected_wells = wells[:4]  # Take first 4 wells
            globals()['selected_wells'] = selected_wells
            print(f"✓ Auto-selected {len(selected_wells)} wells: {[w.petrel_name for w in selected_wells]}")
        except Exception as e:
            print(f"✗ ERROR: Could not get wells: {e}")
            return False
    else:
        selected_wells = globals()['selected_wells']
        print(f"✓ Found {len(selected_wells)} selected wells")
    
    # Check results data
    if 'results' not in globals():
        print("✗ ERROR: No results DataFrame found")
        print("Please ensure ML prediction has been completed")
        return False
    
    results = globals()['results']
    print(f"✓ Results DataFrame found: {results.shape}")
    
    # Step 2: Identify target column
    print("\nStep 2: Identifying target column...")
    
    # Look for VP_COREL related columns
    vp_columns = [col for col in results.columns if 'VP_COREL' in col and ('repredicted' in col or 'imputed' in col)]
    
    if not vp_columns:
        # Look for any imputed/predicted columns
        pred_columns = [col for col in results.columns if ('imputed' in col or 'predicted' in col or 'repredicted' in col)]
        if pred_columns:
            target_column = pred_columns[0]
            print(f"✓ Using prediction column: {target_column}")
        else:
            print("✗ ERROR: No prediction columns found")
            print(f"Available columns: {list(results.columns)}")
            return False
    else:
        target_column = vp_columns[0]
        print(f"✓ Using VP_COREL column: {target_column}")
    
    # Step 3: Find template log
    print("\nStep 3: Finding template log...")
    
    def find_template_log():
        """Find a suitable template log"""
        try:
            # Try common VP log names
            vp_candidates = ['VP', 'VP_COREL', 'Vp', 'P-wave', 'PWAVE']
            
            for candidate in vp_candidates:
                for log in petrel.global_well_logs:
                    if hasattr(log, 'petrel_name') and log.petrel_name == candidate:
                        return log, candidate
            
            # If no VP logs found, use any available log as template
            for log in petrel.global_well_logs:
                if hasattr(log, 'petrel_name'):
                    return log, log.petrel_name
                    
            return None, None
        except Exception as e:
            print(f"Error finding template log: {e}")
            return None, None
    
    template_log, template_name = find_template_log()
    if template_log is None:
        print("✗ ERROR: No template log found")
        return False
    
    print(f"✓ Using template log: {template_name}")
    
    # Step 4: Execute write-back
    print("\nStep 4: Executing write-back...")
    
    # Determine output log name
    if 'VP_COREL_ML_repredicted' in target_column:
        output_log_name = 'VP_COREL_ML_repredicted'
    elif 'repredicted' in target_column:
        output_log_name = target_column
    else:
        output_log_name = f'{target_column}_output'
    
    print(f"Output log name: {output_log_name}")
    
    success_count = 0
    total_wells = len(selected_wells)
    
    for i, well in enumerate(selected_wells):
        print(f"\nProcessing well {i+1}/{total_wells}: {well.petrel_name}")
        
        try:
            # Get data for this well
            well_data = results[results['WELL'] == well.petrel_name]
            if well_data.empty:
                print(f"  ⚠ No data found for {well.petrel_name}")
                continue
            
            # Extract MD and values
            md_values = well_data['MD'].values
            log_values = well_data[target_column].values
            
            # Clean data
            valid_mask = ~np.isnan(md_values) & ~np.isnan(log_values)
            md_clean = md_values[valid_mask]
            values_clean = log_values[valid_mask]
            
            if len(md_clean) == 0:
                print(f"  ⚠ No valid data points for {well.petrel_name}")
                continue
            
            print(f"  Valid data points: {len(md_clean)}")
            
            # Method 1: Try to find existing log in well
            existing_logs = [log for log in well.logs if log.petrel_name == output_log_name]
            
            if existing_logs:
                # Update existing log
                log_obj = existing_logs[0]
                print(f"  Updating existing log: {output_log_name}")
            else:
                # Create new log by cloning template
                template_logs = [log for log in well.logs if log.petrel_name == template_name]
                if template_logs:
                    template_well_log = template_logs[0]
                    log_obj = template_well_log.clone(well, output_log_name)
                    print(f"  Created new log: {output_log_name}")
                else:
                    print(f"  ⚠ No template log {template_name} in well {well.petrel_name}")
                    continue
            
            # Write data to log
            petrel_log_ref = petrel.well_logs[log_obj.path]
            petrel_log_ref.readonly = False
            petrel_log_ref.set_values(md_clean.tolist(), values_clean.tolist())
            
            print(f"  ✓ Successfully wrote {len(md_clean)} values to {output_log_name}")
            success_count += 1
            
        except Exception as e:
            print(f"  ✗ Error processing {well.petrel_name}: {str(e)}")
            import traceback
            print(f"  Debug: {traceback.format_exc()}")
    
    # Step 5: Summary
    print(f"\n{'='*60}")
    print(f"WRITE-BACK SUMMARY")
    print(f"{'='*60}")
    print(f"Target column: {target_column}")
    print(f"Output log name: {output_log_name}")
    print(f"Template log: {template_name}")
    print(f"Wells processed: {success_count}/{total_wells}")
    print(f"Success rate: {success_count/total_wells*100:.1f}%")
    
    if success_count > 0:
        print(f"\n✓ Write-back completed successfully!")
        print(f"Check Petrel for the new '{output_log_name}' logs in your wells.")
    else:
        print(f"\n✗ Write-back failed for all wells.")
        print("Please check the error messages above.")
    
    print(f"{'='*60}")
    return success_count > 0


def simple_writeback(results_df, column_name, wells_list, template_log_name='VP'):
    """
    Simplified write-back function for direct use
    """
    if 'petrel' not in globals():
        print("ERROR: No Petrel connection")
        return False
    
    petrel = globals()['petrel']
    success_count = 0
    
    for well in wells_list:
        try:
            well_data = results_df[results_df['WELL'] == well.petrel_name]
            if well_data.empty:
                continue
                
            md_vals = well_data['MD'].values
            log_vals = well_data[column_name].values
            
            # Clean NaN values
            valid_mask = ~np.isnan(md_vals) & ~np.isnan(log_vals)
            md_clean = md_vals[valid_mask].tolist()
            vals_clean = log_vals[valid_mask].tolist()
            
            if not md_clean:
                continue
            
            # Find or create log
            output_name = column_name if 'repredicted' in column_name else f'{column_name}_output'
            existing = [log for log in well.logs if log.petrel_name == output_name]
            
            if existing:
                log_obj = existing[0]
            else:
                template = [log for log in well.logs if log.petrel_name == template_log_name]
                if template:
                    log_obj = template[0].clone(well, output_name)
                else:
                    continue
            
            # Write data
            petrel_ref = petrel.well_logs[log_obj.path]
            petrel_ref.readonly = False
            petrel_ref.set_values(md_clean, vals_clean)
            
            print(f"✓ {well.petrel_name}: {len(md_clean)} values written")
            success_count += 1
            
        except Exception as e:
            print(f"✗ {well.petrel_name}: {str(e)}")
    
    print(f"Completed: {success_count}/{len(wells_list)} wells")
    return success_count > 0


# Make functions available globally
globals()['execute_writeback'] = execute_writeback
globals()['simple_writeback'] = simple_writeback

print("✓ Write-back functions loaded successfully!")
print("\nTo execute write-back, run:")
print("  execute_writeback()")
print("\nOr for simple usage:")
print("  simple_writeback(results, 'VP_COREL_ML_repredicted', selected_wells)")
