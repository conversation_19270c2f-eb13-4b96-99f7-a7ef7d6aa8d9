{"cells": [{"cell_type": "markdown", "id": "fbad4559", "metadata": {}, "source": ["# Missing Logs Imputation – **Petrel‑connected** workflow with Interactive Selection\n", "\n", "This notebook connects directly to the **currently open Petrel project** with Cegal Prizm Python Tool Pro, provides interactive dropdown menus for well and log selection, pulls the selected global well logs for every well, applies machine‑learning models to fill in missing values, and (optionally) writes the imputed logs back into Petrel.\n", "\n", "*Enhanced with interactive well and log selection - Generated 2025-06-19*."]}, {"cell_type": "code", "execution_count": 3, "id": "1d4e5410", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Connected to Petrel project: ID_BKP_DA_GSD_AVO_ATTR_2022.pet\n"]}], "source": ["# Core libraries\n", "import numpy as np\n", "import pandas as pd\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# ML libraries\n", "from xgboost import XGBRegressor\n", "from lightgbm import LGBMRegressor\n", "from catboost import CatBoostRegressor\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import mean_absolute_error\n", "\n", "# Plotting (optional)\n", "import plotly.express as px\n", "\n", "# Interactive widgets for log selection\n", "import ipywidgets as widgets\n", "from IPython.display import display, clear_output\n", "\n", "# Petrel connection\n", "from cegalprizm.pythontool import PetrelConnection\n", "\n", "petrel = PetrelConnection()\n", "print(f'Connected to Petrel project: {petrel.get_current_project_name()}')"]}, {"cell_type": "markdown", "id": "log_selection_header", "metadata": {}, "source": ["## 1 – Interactive Well and Log Selection"]}, {"cell_type": "code", "execution_count": 7, "id": "log_selection_ui", "metadata": {}, "outputs": [{"ename": "SyntaxError", "evalue": "unexpected character after line continuation character (1512778493.py, line 36)", "output_type": "error", "traceback": ["\u001b[1;36m  Cell \u001b[1;32mIn[7], line 36\u001b[1;36m\u001b[0m\n\u001b[1;33m    wells = [w for w in petrel.wells]\\n,\u001b[0m\n\u001b[1;37m                                      ^\u001b[0m\n\u001b[1;31mSyntaxError\u001b[0m\u001b[1;31m:\u001b[0m unexpected character after line continuation character\n"]}], "source": ["# Create interactive dropdowns for well and log selection\n", "print('Setting up interactive selection interface...')\n", "\n", "# Get all available global well logs\n", "available_logs = {}\n", "for log in petrel.global_well_logs:\n", "    if hasattr(log, 'petrel_name'):\n", "        available_logs[log.petrel_name] = log\n", "    elif isinstance(log, list):\n", "        for sub_log in log:\n", "            if hasattr(sub_log, 'petrel_name'):\n", "                available_logs[sub_log.petrel_name] = sub_log\n", "\n", "log_names = sorted(available_logs.keys())\n", "print(f'Found {len(log_names)} global well logs')\n", "\n", "# Create log selection widgets\n", "input_logs_widget = widgets.SelectMultiple(\n", "    options=log_names,\n", "    value=tuple(['GR', 'RHOB', 'NPHI', 'Vp']) if all(log in log_names for log in ['GR', 'RHOB', 'NPHI', 'Vp']) else tuple(log_names[:4]) if len(log_names) >= 4 else tuple(log_names),\n", "    description='Input Logs:',\n", "    disabled=False,\n", "    layout=widgets.Layout(width='400px', height='120px')\n", ")\n", "\n", "target_log_widget = widgets.Dropdown(\n", "    options=log_names,\n", "    value='Vs' if 'Vs' in log_names else log_names[0] if log_names else None,\n", "    description='Target Log:',\n", "    disabled=False,\n", "    layout=widgets.Layout(width='400px')\n", ")\n", "\n", "# Get all wells and create well selection widget\n", "print('Scanning available wells...')\n", "wells = [w for w in petrel.wells]\\n,\n", "well_names = [w.petrel_name for w in wells]\\n,\n", "print(f'Found {len(wells)} wells')\n", "\n", "# Create well selection widget\n", "wells_widget = widgets.SelectMultiple(\n", "    options=well_names,\n", "    value=tuple(well_names[:min(5, len(well_names))]),  # Select first 5 wells by default\n", "    description='Wells:',\n", "    disabled=False,\n", "    layout=widgets.Layout(width='400px', height='120px')\n", ")\n", "\n", "# Display widgets\n", "print('\\nSelect your wells and logs:')\n", "display(widgets.VBox([\n", "    widgets.HTML('<b><PERSON> Wells (hold Ctrl/Cmd for multiple selection):</b>'),\n", "    wells_widget,\n", "    widgets.HTML('<br><b>Select Input Logs (hold Ctrl/Cmd for multiple selection):</b>'),\n", "    input_logs_widget,\n", "    widgets.HTML('<br><b>Select Target Log for Imputation:</b>'),\n", "    target_log_widget\n", "]))\n", "\n", "# Store widgets for later use\n", "log_selection_widgets = {\n", "    'wells': wells_widget,\n", "    'input_logs': input_logs_widget,\n", "    'target_log': target_log_widget,\n", "    'available_logs': available_logs,\n", "    'all_wells': wells\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "d0edd339", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Currently open Petrel project is ID_BKP_DA_GSD_AVO_ATTR_2022.pet\n"]}], "source": ["ptp = PetrelConnection()\n", "\n", "print(f'Currently open Petrel project is {ptp.get_current_project_name()}')"]}, {"cell_type": "code", "execution_count": null, "id": "d7c296fa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Currently open Petrel project is ID_BKP_DA_GSD_AVO_ATTR_2022.pet\n"]}], "source": ["ptp = PetrelConnection()\n", "\n", "print(f'Currently open Petrel project is {ptp.get_current_project_name()}')"]}, {"cell_type": "markdown", "id": "wells_config", "metadata": {}, "source": ["## 2 – Configure wells & apply selection"]}, {"cell_type": "code", "execution_count": 8, "id": "b48fcdd1", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'wells_widget' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[8], line 2\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;66;03m# Get selected wells from widget\u001b[39;00m\n\u001b[1;32m----> 2\u001b[0m selected_well_names \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlist\u001b[39m(\u001b[43mwells_widget\u001b[49m\u001b[38;5;241m.\u001b[39mvalue)\n\u001b[0;32m      3\u001b[0m selected_wells \u001b[38;5;241m=\u001b[39m [w \u001b[38;5;28;01mfor\u001b[39;00m w \u001b[38;5;129;01min\u001b[39;00m wells \u001b[38;5;28;01mif\u001b[39;00m w\u001b[38;5;241m.\u001b[39mpetrel_name \u001b[38;5;129;01min\u001b[39;00m selected_well_names]\n\u001b[0;32m      4\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mSelected \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mlen\u001b[39m(selected_wells)\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m wells: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m[w\u001b[38;5;241m.\u001b[39mpetrel_name\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mfor\u001b[39;00m\u001b[38;5;250m \u001b[39mw\u001b[38;5;250m \u001b[39m\u001b[38;5;129;01min\u001b[39;00m\u001b[38;5;250m \u001b[39mselected_wells]\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m)\n", "\u001b[1;31mNameError\u001b[0m: name 'wells_widget' is not defined"]}], "source": ["# Get selected wells from widget\n", "selected_well_names = list(wells_widget.value)\n", "selected_wells = [w for w in wells if w.petrel_name in selected_well_names]\n", "print(f'Selected {len(selected_wells)} wells: {[w.petrel_name for w in selected_wells]}')\n", "\n", "# Get selected logs from widgets\n", "selected_input_logs = list(input_logs_widget.value)\n", "selected_target_log = target_log_widget.value\n", "\n", "print(f'Selected input logs: {selected_input_logs}')\n", "print(f'Selected target log: {selected_target_log}')\n", "\n", "# Combine all log names\n", "LOG_NAMES = selected_input_logs + [selected_target_log]\n", "print(f'All logs to process: {LOG_NAMES}')\n", "\n", "# Helper function to find global well logs by name\n", "def find_global_well_logs_by_names(names):\n", "    found_logs = []\n", "    for name in names:\n", "        if name in available_logs:\n", "            found_logs.append(available_logs[name])\n", "        else:\n", "            print(f'Warning: Log {name} not found in available logs')\n", "    return found_logs\n", "\n", "logs = find_global_well_logs_by_names(LOG_NAMES)\n", "print(f'Using {len(logs)} global logs: {[g.petrel_name for g in logs]}')\n", "\n", "# Validate that we have both wells and logs selected\n", "if not selected_wells:\n", "    print('ERROR: No wells selected! Please select wells in the dropdown above.')\n", "if not logs:\n", "    print('ERROR: No valid logs found! Please check your log selection.')\n", "if selected_wells and logs:\n", "    print('✓ Ready to load data from selected wells and logs')"]}, {"cell_type": "markdown", "id": "9112c3f8", "metadata": {}, "source": ["## 3 – Load log data from Petrel (Fixed with <PERSON><PERSON><PERSON>)"]}, {"cell_type": "code", "execution_count": null, "id": "75e81177", "metadata": {}, "outputs": [], "source": ["# Ensure we have the required variables from previous cells\n", "try:\n", "    # Check if variables exist from section 2\n", "    if 'selected_wells' not in locals():\n", "        print('Getting wells and logs from widgets...')\n", "        selected_well_names = list(wells_widget.value)\n", "        selected_wells = [w for w in wells if w.petrel_name in selected_well_names]\n", "        \n", "    if 'logs' not in locals():\n", "        selected_input_logs = list(input_logs_widget.value)\n", "        selected_target_log = target_log_widget.value\n", "        LOG_NAMES = selected_input_logs + [selected_target_log]\n", "        \n", "        def find_global_well_logs_by_names(names):\n", "            found_logs = []\n", "            for name in names:\n", "                if name in available_logs:\n", "                    found_logs.append(available_logs[name])\n", "            return found_logs\n", "        \n", "        logs = find_global_well_logs_by_names(LOG_NAMES)\n", "        \n", "    print(f'Loading data from {len(selected_wells)} wells with {len(logs)} logs...')\n", "    \n", "    # Initialize empty DataFrame\n", "    well_data = pd.DataFrame()\n", "    \n", "    # Load data from each selected well\n", "    for i, w in enumerate(selected_wells):\n", "        print(f'Processing well {i+1}/{len(selected_wells)}: {w.petrel_name}')\n", "        try:\n", "            df = w.logs_dataframe(logs)      # returns MD‑indexed DataFrame\n", "            if not df.empty:\n", "                df['WELL'] = w.petrel_name\n", "                well_data = pd.concat([well_data, df], ignore_index=False)\n", "                print(f'  ✓ Loaded {len(df)} samples')\n", "            else:\n", "                print(f'  ⚠ No data found for this well')\n", "        except Exception as e:\n", "            print(f'  ✗ Error loading data: {str(e)}')\n", "    \n", "    # Reset index to make MD a column\n", "    if not well_data.empty:\n", "        well_data.reset_index(drop=False, inplace=True)  # MD becomes column\n", "        print(f'\\n✓ Combined DataFrame shape: {well_data.shape}')\n", "        print(f'Columns: {list(well_data.columns)}')\n", "        display(well_data.head())\n", "    else:\n", "        print('\\n✗ No data loaded! Check your well and log selections.')\n", "        \n", "except Exception as e:\n", "    print(f'Error in data loading: {str(e)}')\n", "    print('Make sure you have run the previous cells to select wells and logs.')"]}, {"cell_type": "markdown", "id": "76eebf83", "metadata": {}, "source": ["## 4 – Basic cleaning (remove obvious spikes)"]}, {"cell_type": "code", "execution_count": null, "id": "47becf17", "metadata": {}, "outputs": [], "source": ["# Clean GR if it's in the selected logs\n", "if 'GR' in well_data.columns:\n", "    GR_MAX = 300\n", "    well_data['GR'] = np.where(well_data['GR'] <= GR_MAX, well_data['GR'], np.nan)\n", "    print(f'Cleaned GR values > {GR_MAX}')\n", "    \n", "# Clean NPHI if it's in the selected logs\n", "if 'NPHI' in well_data.columns:\n", "    well_data['NPHI'] = np.where(\n", "        (well_data['NPHI'] >= 0) & (well_data['NPHI'] <= 1),\n", "        well_data['NPHI'], np.nan\n", "    )\n", "    print('Cleaned NPHI values outside [0, 1] range')\n", "    \n", "# Clean RHOB if it's in the selected logs\n", "if 'RHOB' in well_data.columns:\n", "    well_data['RHOB'] = np.where(\n", "        (well_data['RHOB'] >= 1.0) & (well_data['RHOB'] <= 3.5),\n", "        well_data['RHOB'], np.nan\n", "    )\n", "    print('Cleaned RHOB values outside [1.0, 3.5] range')\n", "    \n", "print('\\nData summary after cleaning:')\n", "display(well_data.describe().T)"]}, {"cell_type": "markdown", "id": "c307e868", "metadata": {}, "source": ["## 5 – Coverage per log"]}, {"cell_type": "code", "execution_count": null, "id": "edb6534b", "metadata": {}, "outputs": [], "source": ["# Calculate coverage for selected logs only\n", "log_columns = [col for col in LOG_NAMES if col in well_data.columns]\n", "coverage = 1.0 - well_data[log_columns].isna().mean()\n", "print('Data coverage per log:')\n", "for log_name, cov in coverage.items():\n", "    print(f'  {log_name}: {cov:.2%}')\n", "\n", "# Visualize coverage\n", "fig = px.bar(coverage, labels={'value':'Coverage'}, title='Relative data coverage for selected logs')\n", "fig.show()"]}, {"cell_type": "markdown", "id": "4082c93a", "metadata": {}, "source": ["## 6 – Imputation utility with enhanced ML models"]}, {"cell_type": "code", "execution_count": null, "id": "d204d2a3", "metadata": {}, "outputs": [], "source": ["def impute_logs(df, depth_col, feature_cols, targets):\n", "    \"\"\"Return DataFrame with *_pred, *_imputed, *_error columns.\"\"\"\n", "    res = df.copy()\n", "    # Configure ML models with user's preferred hyperparameters\n", "    boosters = [\n", "        ('EXTREME BOOST REGRESSOR', XGBRegressor(\n", "            n_estimators=300,\n", "            tree_method='gpu_hist',\n", "            learning_rate=0.05,\n", "            early_stopping_rounds=100,\n", "            random_state=42\n", "        )),\n", "        ('LGBM REGRESSOR', LGBMRegressor(\n", "            device='gpu',\n", "            gpu_platform_id=1,\n", "            gpu_device_id=0,\n", "            n_estimators=300,\n", "            random_state=42\n", "        )),\n", "        ('CATBOOST REGRESSOR', CatBoostRegressor(\n", "            task_type='GPU',\n", "            early_stopping_rounds=100,\n", "            verbose=0,\n", "            random_state=42\n", "        ))\n", "    ]\n", "    feature_set = feature_cols + [depth_col]\n", "\n", "    for tgt in targets:\n", "        print(f'--- Imputing {tgt} ---')\n", "        train = res[res[tgt].notna()][feature_set + [tgt]].copy()\n", "        if train.empty:\n", "            print('No training data, skipping.')\n", "            continue\n", "        X = train.drop(columns=[tgt]).apply(lambda c: c.fillna(c.mean()), axis=0)\n", "        y = train[tgt]\n", "\n", "        Xtr, Xval, ytr, yval = train_test_split(X, y, test_size=0.25, random_state=42)\n", "        best_model, best_name, best_mae = None, None, float(\"inf\")\n", "        \n", "        print(f'Training {len(boosters)} models on {len(Xtr)} samples...')\n", "        for name, model in boosters:\n", "            try:\n", "                model.fit(Xtr, ytr)\n", "                mae = mean_absolute_error(yval, model.predict(Xval))\n", "                print(f'  {name}: MAE = {mae:.3f}')\n", "                if mae < best_mae:\n", "                    best_model, best_name, best_mae = model, name, mae\n", "            except Exception as e:\n", "                print(f'  {name}: Failed - {str(e)}')\n", "                \n", "        if best_model is None:\n", "            print('All models failed! Skipping this target.')\n", "            continue\n", "            \n", "        print(f'✓ Best model: {best_name} (MAE={best_mae:.3f})')\n", "\n", "        X_full = res[feature_set].apply(lambda c: c.fillna(c.mean()), axis=0)\n", "        preds = best_model.predict(X_full)\n", "        res[f'{tgt}_pred'] = preds\n", "        res[f'{tgt}_imputed'] = res[tgt].fillna(preds)\n", "        res[f'{tgt}_error'] = np.abs(res[tgt] - preds) / res[tgt] * 100\n", "        \n", "        # Show imputation statistics\n", "        missing_count = res[tgt].isna().sum()\n", "        total_count = len(res)\n", "        print(f'Imputed {missing_count}/{total_count} missing values ({missing_count/total_count:.1%})')\n", "        \n", "    return res"]}, {"cell_type": "markdown", "id": "e92911f8", "metadata": {}, "source": ["## 7 – Run imputation with selected logs"]}, {"cell_type": "code", "execution_count": null, "id": "254a2a95", "metadata": {}, "outputs": [], "source": ["DEPTH_COL = 'MD'\n", "FEATURES = selected_input_logs  # Use selected input logs\n", "TARGET = selected_target_log    # Use selected target log\n", "\n", "print(f'Running imputation for target: {TARGET}')\n", "print(f'Using features: {FEATURES}')\n", "print(f'Depth column: {DEPTH_COL}')\n", "\n", "# Run the imputation\n", "results = impute_logs(well_data, DEPTH_COL, FEATURES, targets=[TARGET])\n", "\n", "print(f'\\nImputation completed! Results shape: {results.shape}')\n", "print(f'New columns added: {[col for col in results.columns if col.endswith(\"_pred\") or col.endswith(\"_imputed\") or col.endswith(\"_error\")]}')\n", "\n", "display(results.head())"]}, {"cell_type": "markdown", "id": "a40f1ffa", "metadata": {}, "source": ["## 8 – (Option) Write imputed logs back to Petrel"]}, {"cell_type": "code", "execution_count": null, "id": "7ab4c50b", "metadata": {}, "outputs": [], "source": ["def write_back_to_petrel(results_df, log_name_in_results, clone_from=None):\n", "    \"\"\"Clone an existing log (or first available log) and overwrite with imputed values.\"\"\"\n", "    # Ensure we have the selected wells\n", "    if 'selected_wells' not in locals():\n", "        selected_well_names = list(wells_widget.value)\n", "        selected_wells = [w for w in wells if w.petrel_name in selected_well_names]\n", "    \n", "    if clone_from is None:\n", "        clone_from = target_log_widget.value  # Use the selected target log as template\n", "        \n", "    print(f'Writing back {log_name_in_results} to {len(selected_wells)} wells...')\n", "    success_count = 0\n", "    \n", "    for w in selected_wells:\n", "        print(f'Updating {w.petrel_name}')\n", "        well_df = results_df[results_df['WELL'] == w.petrel_name].set_index('MD')\n", "        if well_df.empty:\n", "            print(f'  ⚠ No data for well {w.petrel_name}, skipping')\n", "            continue\n", "            \n", "        md = well_df.index.to_numpy()\n", "        values = well_df[log_name_in_results].to_numpy()\n", "\n", "        # Find a log to clone (or existing target)\n", "        target_logs = [log for log in w.logs if log.petrel_name == log_name_in_results]\n", "        if target_logs:\n", "            log_obj = target_logs[0]\n", "            print(f'  Using existing log {log_name_in_results}')\n", "        else:\n", "            # Clone template log\n", "            template_logs = [log for log in w.logs if log.petrel_name == clone_from]\n", "            if template_logs:\n", "                template = template_logs[0]\n", "                log_obj = template.clone(w, log_name_in_results)\n", "                print(f'  Created new log {log_name_in_results} from template {clone_from}')\n", "            else:\n", "                print(f'  ✗ No template log {clone_from} found in well {w.petrel_name}, skipping')\n", "                continue\n", "\n", "        try:\n", "            petrel_log_ref = petrel.well_logs[log_obj.path]\n", "            petrel_log_ref.readonly = False\n", "            petrel_log_ref.set_values(md, values)\n", "            print(f'  ✓ Successfully updated {log_name_in_results} for {w.petrel_name}')\n", "            success_count += 1\n", "        except Exception as e:\n", "            print(f'  ✗ Error updating {log_name_in_results} for {w.petrel_name}: {str(e)}')\n", "    \n", "    print(f'\\nWrite-back completed: {success_count}/{len(selected_wells)} wells updated successfully.')\n", "\n", "# Example usage - uncomment to execute\n", "try:\n", "    imputed_log_name = f'{target_log_widget.value}_imputed'\n", "    print(f'To write back imputed values, run:')\n", "    print(f'write_back_to_petrel(results, \\'{imputed_log_name}\\')')\n", "    print('\\nUncomment the line below to execute:')\n", "    # write_back_to_petrel(results, imputed_log_name)\n", "except:\n", "    print('Run the log selection cells first to enable write-back functionality.')"]}], "metadata": {"kernelspec": {"display_name": "PrizmEnv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 5}